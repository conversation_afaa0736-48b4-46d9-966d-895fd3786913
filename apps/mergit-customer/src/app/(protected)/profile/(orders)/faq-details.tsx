import { View, Text } from 'react-native';
import React, { useState } from 'react';
import { Header } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { CustomBtn } from '@mergit-mobile/components';
import { CallConfirmation } from 'components/profile/faq';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const FAQDetail = () => {
    const { title, description } = useLocalSearchParams();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const handleRequestCall = () => {
        setIsModalVisible(true);
    };

    return (
        <View className="flex-1 bg-white" style={{ paddingBottom: bottomInset ?? 28 }}>
            <Header backArrow title="Get Help" shadowBottom />

            <View className="flex-1 gap-5 p-primary">
                <Text
                    style={fonts.fontSemiBold}
                    className="text-text16 tracking-wide text-secondary">
                    {title}
                </Text>
                <Text style={fonts.fontRegular} className="text-text15 text-secondary">
                    {description}
                </Text>
                <View className="h-[0.5] w-full bg-border " />
                <View className="gap-5">
                    <Text
                        style={fonts.fontSemiBold}
                        className="test-text16 tracking-wide text-secondary">
                        Still Need Support?
                    </Text>
                    <View className="items-start">
                        <CustomBtn
                            onPress={handleRequestCall}
                            title={t('request_callback')}
                            icon={<Ionicons name="call-outline" size={18} color="#fff" />}
                            textStyle={{
                                fontSize: 15,
                                letterSpacing: 0.3,
                                color: '#fff',
                            }}
                            height={48}
                        />
                    </View>
                </View>
            </View>

            <CallConfirmation isVisible={isModalVisible} setIsVisible={setIsModalVisible} />
        </View>
    );
};

export default FAQDetail;
