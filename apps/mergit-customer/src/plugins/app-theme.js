const { withAndroidStyles } = require('@expo/config-plugins');

module.exports = function withAppTheme(config) {
    return withAndroidStyles(config, (config) => {
        if (!config.modResults.resources.style) {
            config.modResults.resources.style = [];
        }

        const newTheme = {
            $: { name: 'AppTheme', parent: 'Theme.EdgeToEdge' },
            item: [
                { _: '@android:color/white', $: { name: 'android:navigationBarColor' } },
                {
                    _: 'false',
                    $: { name: 'android:enforceNavigationBarContrast', 'tools:targetApi': 'q' },
                },
                { _: 'false', $: { name: 'android:windowTranslucentNavigation' } },
                { _: 'false', $: { name: 'android:windowIsTranslucent' } },
                {
                    _: 'shortEdges',
                    $: { name: 'android:windowLayoutInDisplayCutoutMode', 'tools:targetApi': 'p' },
                },
                { _: 'true', $: { name: 'android:windowLightStatusBar' } },
            ],
        };

        const existingIndex = config.modResults.resources.style.findIndex(
            (style) => style.$.name === 'AppTheme'
        );
        if (existingIndex > -1) {
            config.modResults.resources.style[existingIndex] = newTheme;
        } else {
            config.modResults.resources.style.push(newTheme);
        }

        return config;
    });
};
