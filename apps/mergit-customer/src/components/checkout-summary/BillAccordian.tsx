import { MaterialIcons } from '@expo/vector-icons';
import { shadow, fonts } from '@mergit-mobile/styles';
import { Bill } from 'assets/icons/checkout-summary';
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Pressable, Modal } from 'react-native';

const BillAccordion = ({ setIsExpanded, isExpanded }: any) => {
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedInfoLabel, setSelectedInfoLabel] = useState<string | null>(null);

    const toggleAccordion = () => setIsExpanded((prev: any) => !prev);

    const billDetails = [
        { label: 'Item Total for shop name', value: '₹655' },
        {
            label: 'Discounted Item Total',
            value: '₹655',
            originalValue: '₹655',
        },
        {
            label: 'GST and Shop Charges',
            value: '₹19',
            originalValue: '₹655',
        },
        {
            label: 'Delivery Charges',
            value: '₹40',
            originalValue: '₹655',
        },
        {
            label: 'Platform Fee',
            value: '₹10',
            originalValue: '₹655',
        },
    ];

    const totalDetails = [
        { label: 'Grand Total', value: '₹655' },
        { label: 'To Pay', value: '₹655' },
    ];

    const InfoText = ({
        label,
        originalValue,
        onPressInfo,
    }: {
        label: string;
        originalValue: any;
        onPressInfo: () => void;
    }) => {
        const isInfoLabel = ['GST and Shop Charges', 'Delivery Charges', 'Platform Fee'].includes(
            label
        );

        return (
            <Pressable disabled={!isInfoLabel} onPress={onPressInfo}>
                <Text
                    className={`text-text14 ${
                        originalValue ? 'text-neutral' : 'text-secondary'
                    } ${isInfoLabel ? 'underline decoration-dotted' : ''}`}
                    style={!originalValue ? fonts.fontSemiBold : fonts.fontRegular}>
                    {label}
                </Text>
            </Pressable>
        );
    };

    const renderRow = ({
        label,
        value,
        originalValue,
    }: {
        label: string;
        value: string;
        originalValue?: string;
    }) => (
        <View className="flex-row justify-between py-2" key={label}>
            <InfoText
                label={label}
                originalValue={originalValue}
                onPressInfo={() => {
                    setSelectedInfoLabel(label);
                    setModalVisible(true);
                }}
            />
            <View className="flex-row items-center gap-2">
                {originalValue && (
                    <Text
                        style={fonts.fontMedium}
                        className="text-text12 text-neutral line-through">
                        {originalValue}
                    </Text>
                )}
                <Text style={fonts.fontSemiBold} className="text-text14 text-secondary">
                    {value}
                </Text>
            </View>
        </View>
    );

    const renderModalContent = () => {
        if (selectedInfoLabel === 'GST and Shop Charges') {
            return (
                <View className="gap-3 p-4">
                    <Text style={fonts.fontMedium} className="mb-3 text-justify text-grayText">
                        Mergit has no role to play in taxes levied by the govt. and charges decided
                        by shops.
                    </Text>
                    <View className="flex-row justify-between">
                        <Text style={fonts.fontMedium} className=" text-text14 text-secondary">
                            Kovai Pazhamudir Nilayam
                        </Text>
                        <Text style={fonts.fontMedium} className=" text-text14 text-secondary">
                            ₹37.57
                        </Text>
                    </View>
                    <View className="flex-row justify-between">
                        <Text style={fonts.fontMedium} className=" text-text14 text-grayText">
                            GST on item total
                        </Text>
                        <Text style={fonts.fontMedium} className=" text-text14 text-grayText">
                            ₹12.45
                        </Text>
                    </View>
                    <View className="flex-row justify-between">
                        <Text style={fonts.fontMedium} className=" text-text14 text-grayText">
                            Shop packaging charges
                        </Text>
                        <Text style={fonts.fontMedium} className=" text-text14 text-grayText">
                            ₹23.92
                        </Text>
                    </View>
                    <View className="flex-row justify-between">
                        <Text style={fonts.fontMedium} className=" text-text14 text-grayText">
                            GST on shop packaging charges
                        </Text>
                        <Text style={fonts.fontMedium} className=" text-text14 text-grayText">
                            ₹1.20
                        </Text>
                    </View>
                    <View className="my-2 border-t border-gray-300" />
                    <View className="flex-row justify-between">
                        <Text style={fonts.fontMedium} className=" text-text14 text-secondary">
                            Total
                        </Text>
                        <Text style={fonts.fontMedium} className=" text-text14 text-secondary">
                            ₹54.69
                        </Text>
                    </View>
                </View>
            );
        } else if (selectedInfoLabel === 'Delivery Charges') {
            return (
                <View className="gap-2 p-4">
                    <View className="flex-row justify-between">
                        <Text style={fonts.fontMedium} className=" text-text14 text-secondary">
                            Kovai Pazhamudir Nilayam
                        </Text>
                        <Text style={fonts.fontMedium} className="text-text14 text-secondary">
                            ₹0
                        </Text>
                    </View>
                    <View className="flex-row justify-between">
                        <Text className=" text-text14 text-grayText" style={fonts.fontMedium}>
                            Base Fee
                        </Text>
                        <Text className=" text-text14 text-grayText" style={fonts.fontMedium}>
                            ₹33
                        </Text>
                    </View>
                    <View className="flex-row justify-between">
                        <Text className=" text-text14 text-grayText" style={fonts.fontMedium}>
                            Small Order Fee
                        </Text>
                        <Text className=" text-text14 text-grayText" style={fonts.fontMedium}>
                            ₹0
                        </Text>
                    </View>
                    <View className="flex-row justify-between ">
                        <Text style={fonts.fontMedium} className="text-primary">
                            Mergit discount on delivery fee
                        </Text>
                        <Text style={fonts.fontMedium} className="text-primary">
                            -₹33
                        </Text>
                    </View>
                </View>
            );
        } else if (selectedInfoLabel === 'Platform Fee') {
            return (
                <View className="gap-2 p-4">
                    <Text style={fonts.fontMedium} className=" text-text17 text-secondary">
                        {selectedInfoLabel}
                    </Text>
                    <Text
                        style={fonts.fontMedium}
                        className="text-justify text-text14 text-neutral">
                        This small fee helps us pay the bills so that we can keep Mergit running.
                    </Text>
                </View>
            );
        } else {
            return null;
        }
    };

    return (
        <View className="px-primary">
            <View
                style={shadow}
                className="overflow-hidden rounded-xl border border-border bg-white">
                {/* Main Accordion */}
                <TouchableOpacity
                    onPress={toggleAccordion}
                    activeOpacity={0.7}
                    className="flex-row items-center gap-3 p-4">
                    <Bill />
                    <View className="flex-1 gap-1">
                        <Text
                            style={fonts.fontBold}
                            className="text-base text-text14 text-secondary">
                            Bill Amount - ₹300
                        </Text>
                        <Text className="text-text13 text-neutral">₹50 saved on this order</Text>
                    </View>
                    <MaterialIcons
                        name={isExpanded ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
                        size={24}
                        color="#414651"
                    />
                </TouchableOpacity>

                {isExpanded && (
                    <View className="border-t border-border p-4">
                        {billDetails.map(renderRow)}
                        <View className="my-2 h-[0.5] w-full bg-border" />
                        {totalDetails.map(renderRow)}
                    </View>
                )}

                {/* Modal */}
                <Modal
                    animationType="fade"
                    transparent={true}
                    visible={modalVisible}
                    statusBarTranslucent
                    navigationBarTranslucent
                    onRequestClose={() => setModalVisible(false)}>
                    <View className="flex-1 items-center justify-center bg-black/50 bg-opacity-50">
                        <View className="w-11/12 max-w-md rounded-xl bg-white">
                            {renderModalContent()}
                            <TouchableOpacity
                                activeOpacity={0.8}
                                onPress={() => setModalVisible(false)}
                                className="rounded-b-xl border-t border-border py-3">
                                <Text
                                    style={fonts.fontBold}
                                    className="text-center text-base text-primary">
                                    OKAY
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
            </View>
        </View>
    );
};

export default BillAccordion;
