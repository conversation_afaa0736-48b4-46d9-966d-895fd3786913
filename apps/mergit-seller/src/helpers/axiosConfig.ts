import axios from 'axios';
import { useClientStore } from '~/services/client';

export const axiosInstance = axios.create({
    baseURL: process.env.EXPO_PUBLIC_API_URL!,
});

let isRefreshing = false;
let subscribers: ((token: string) => void)[] = [];
let refreshInterval: NodeJS.Timeout | null = null;

const onRefreshed = (token: string) => {
    subscribers.forEach((cb) => cb(token));
    subscribers = [];
};

const addSubscriber = (callback: (token: string) => void) => {
    subscribers.push(callback);
};

const refreshTokenFn = async () => {
    const refreshToken = useClientStore.getState().refreshToken;
    if (!refreshToken) return null;

    try {
        const { data } = await axios.post(
            `${process.env.EXPO_PUBLIC_API_URL!}/auth/v1/auth-service/refresh`,
            { refreshToken }
        );

        const newToken = data?.accessToken;
        if (!newToken) return null;

        useClientStore.getState().login(newToken);
        useClientStore.getState().setAuthToken(newToken);

        onRefreshed(newToken);
        return newToken;
    } catch (err) {
        useClientStore.getState().logout();
        useClientStore.getState().removeAuthToken();
        console.log('fetching refresh token error : ', err);
        throw err;
    }
};

export const startTokenRefreshScheduler = () => {
    if (refreshInterval) clearInterval(refreshInterval);

    refreshInterval = setInterval(
        () => {
            refreshTokenFn().catch((err) => {
                console.log('Background token refresh failed', err);
            });
        },
        15 * 60 * 1000
    );
};

export const stopTokenRefreshScheduler = () => {
    if (refreshInterval) clearInterval(refreshInterval);
    refreshInterval = null;
};

axiosInstance.interceptors.request.use(
    async (request: any) => {
        const token = useClientStore.getState().token;

        const headers = {
            'Content-Type': 'application/json',
            ...request.headers,
        };

        if (token) {
            headers.Authorization = `Bearer ${token}`;
        }

        request.headers = headers;
        return request;
    },
    (error) => Promise.reject(error)
);

axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        if (error?.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            if (!isRefreshing) {
                isRefreshing = true;

                try {
                    const newToken = await refreshTokenFn();
                    return axiosInstance({
                        ...originalRequest,
                        headers: {
                            ...originalRequest.headers,
                            Authorization: `Bearer ${newToken}`,
                        },
                    });
                } catch (err) {
                    return Promise.reject(err);
                } finally {
                    isRefreshing = false;
                }
            }

            return new Promise((resolve) => {
                addSubscriber((token: string) => {
                    originalRequest.headers.Authorization = `Bearer ${token}`;
                    resolve(axiosInstance(originalRequest));
                });
            });
        }

        return Promise.reject(error?.response?.data ?? error);
    }
);
