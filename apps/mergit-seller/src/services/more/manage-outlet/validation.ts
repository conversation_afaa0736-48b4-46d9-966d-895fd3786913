import * as yup from 'yup';

export const shopNameValidationSchema = yup.object({
    shopName: yup
        .string()
        .required('Shop Name is required')
        .matches(/^[A-Za-z0-9 ]+$/, 'Shop Name can only contain letters, digits and spaces')
        .min(3, 'Shop Name must be at least 3 characters')
        .max(50, 'Shop Name can be at most 50 characters'),
});

export const shopAddressSchema = yup.object().shape({
    shopNo: yup.string().required('Shop No / Building No is required'),
    floor: yup.string().required('Floor / Tower is required'),
    area: yup.string().required('Area / Locality is required'),
    city: yup.string().required('City is required'),
    landmark: yup.string().required('Landmark is required'),
});
