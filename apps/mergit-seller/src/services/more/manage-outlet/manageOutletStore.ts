import { create } from 'zustand';

type OutletState = {
    outletImage: string | null;
    logoImage: string | null;
    selectedTypes: string[];
    shopName: string;
    shopAddress: string;
    setOutletImage: (uri: string) => void;
    setLogoImage: (uri: string) => void;
    setShopName: (name: string) => void;
    setSelectedTypes: (types: string[]) => void;
    addType: (type: string) => void;
    removeType: (type: string) => void;
    selectedCuisines: string[];
    setSelectedCuisines: (items: string[]) => void;
    addCuisine: (cuisine: string) => void;
    removeCuisine: (cuisine: string) => void;
};

export const useOutletStore = create<OutletState>((set) => ({
    outletImage: 'https://uniquekiosk.com/wp-content/uploads/2020/04/2-31.jpg',
    logoImage: null,
    shopName: 'SS Hyderabad Biriyani',
    shopAddress: '107, Intecai digital limited, Chennai',
    selectedTypes: ['veg', 'nonVeg'],
    setOutletImage: (uri) => set({ outletImage: uri }),
    setLogoImage: (uri) => set({ logoImage: uri }),
    setShopName: (name) => set({ shopName: name }),
    setSelectedTypes: (types) => set({ selectedTypes: types }),
    addType: (type) =>
        set((state) =>
            state.selectedTypes.includes(type)
                ? state
                : { selectedTypes: [...state.selectedTypes, type] }
        ),
    removeType: (type) =>
        set((state) => ({
            selectedTypes: state.selectedTypes.filter((t) => t !== type),
        })),
    selectedCuisines: ['Briyani', 'North Indian'], // ✅ default selection
    setSelectedCuisines: (items) => set({ selectedCuisines: items }),
    addCuisine: (cuisine) =>
        set((state) =>
            state.selectedCuisines.includes(cuisine)
                ? state
                : { selectedCuisines: [...state.selectedCuisines, cuisine] }
        ),
    removeCuisine: (cuisine) =>
        set((state) => ({
            selectedCuisines: state.selectedCuisines.filter((c) => c !== cuisine),
        })),
}));
