import * as yup from 'yup';

export const personalInfoSchema = yup.object({
    mobile: yup
        .string()
        .matches(/^[0-9]{10}$/, 'Mobile number must be exactly 10 digits')
        .required('Mobile number is required'),
    email: yup
        .string()
        .required('Email is required')
        .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Enter a valid email address'),
    name: yup
        .string()
        .required('Name is required')
        .matches(/^[A-Za-z ]+$/, 'Name can only contain letters and spaces')
        .min(3, 'Name must be at least 3 characters')
        .max(50, 'Name can be at most 50 characters'),
});
