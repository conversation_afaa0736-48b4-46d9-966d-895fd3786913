import { create } from 'zustand';

interface PersonalInfoState {
    mobile: string;
    email: string;
    name: string;
    setPersonalInfo: (data: Partial<PersonalInfoState>) => void;
    resetPersonalInfo: () => void;
}

export const usePersonalInfoStore = create<PersonalInfoState>((set) => ({
    mobile: '9876543210',
    email: '<EMAIL>',
    name: '<PERSON>',
    setPersonalInfo: (data) => set((state) => ({ ...state, ...data })),
    resetPersonalInfo: () =>
        set({
            mobile: '9876543210',
            email: '<EMAIL>',
            name: '<PERSON>',
        }),
}));
