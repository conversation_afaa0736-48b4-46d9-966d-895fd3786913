import { create } from 'zustand';

export interface OrderHistoryState {
    filters: string[] | null;
    setFilters: (filters: string[] | null) => void;

    selectedValue: string;
    setSelectedValue: (selectedValue: string) => void;

    showFilter: boolean;
    setShowFilter: (showFilter: boolean) => void;
}

export const useOrderHistoryStore = create<OrderHistoryState>((set) => ({
    filters: null,
    setFilters: (filters) => set({ filters }),

    selectedValue: 'all',
    setSelectedValue: (selectedValue) => set({ selectedValue }),

    showFilter: false,
    setShowFilter: (showFilter) => set({ showFilter }),
}));
