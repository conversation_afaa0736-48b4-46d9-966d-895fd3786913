import { create } from 'zustand';

interface CommunicationState {
    whatsappNotifications: boolean;
    dailyReportWhatsapp: boolean;
    dailyReportEmail: boolean;
    weeklyReportWhatsapp: boolean;
    weeklyReportEmail: boolean;
    orderNotifications: boolean;
    ringInSilentMode: boolean;
    liveComplaintNotification: boolean;
    riderNotification: boolean;
    setCommunicationSettings: (settings: Partial<CommunicationState>) => void;
    resetCommunicationSettings: () => void;
}

export const useCommunicationStore = create<CommunicationState>((set) => ({
    whatsappNotifications: true,
    dailyReportWhatsapp: true,
    dailyReportEmail: true,
    weeklyReportWhatsapp: true,
    weeklyReportEmail: true,
    orderNotifications: true,
    ringInSilentMode: true,
    liveComplaintNotification: true,
    riderNotification: true,
    setCommunicationSettings: (settings) => set((state) => ({ ...state, ...settings })),
    resetCommunicationSettings: () =>
        set({
            whatsappNotifications: true,
            dailyReportWhatsapp: true,
            dailyReportEmail: true,
            weeklyReportWhatsapp: true,
            weeklyReportEmail: true,
            orderNotifications: true,
            ringInSilentMode: true,
            liveComplaintNotification: true,
            riderNotification: true,
        }),
}));

// Outlet Timing

type TimeSlot = {
    id: number;
    open: string;
    close: string;
};

export type DaySlot = {
    id: number;
    day: string;
    isOpen: boolean;
    slots: TimeSlot[];
};

type OutletTimingState = {
    timeSlotData: DaySlot[];
    toggleDayOpen: (dayId: number) => void;
    addSlot: (dayId: number, slot: TimeSlot[]) => void;
    editSlot: (dayId: number, slotIndex: number, updatedSlot: TimeSlot) => void;
    deleteSlot: (dayId: number, slotIndex: number) => void;
};

export const useOutletTimingStore = create<OutletTimingState>((set) => ({
    timeSlotData: [
        {
            id: 1,
            day: 'Monday',
            isOpen: true,
            slots: [
                { id: 1, open: '10:00 AM', close: '1:00 PM' },
                { id: 2, open: '5:00 PM', close: '10:00 PM' },
            ],
        },
        {
            id: 2,
            day: 'Tuesday',
            isOpen: true,
            slots: [{ id: 1, open: '10:00 AM', close: '10:00 PM' }],
        },
        {
            id: 3,
            day: 'Wednesday',
            isOpen: true,
            slots: [{ id: 1, open: '10:00 AM', close: '10:00 PM' }],
        },
        {
            id: 4,
            day: 'Thursday',
            isOpen: true,
            slots: [{ id: 1, open: '10:00 AM', close: '10:00 PM' }],
        },
        {
            id: 5,
            day: 'Friday',
            isOpen: true,
            slots: [
                { id: 1, open: '10:00 AM', close: '1:00 PM' },
                { id: 2, open: '6:00 PM', close: '11:00 PM' },
            ],
        },
        {
            id: 6,
            day: 'Saturday',
            isOpen: true,
            slots: [{ id: 1, open: '10:00 AM', close: '10:00 PM' }],
        },
        {
            id: 7,
            day: 'Sunday',
            isOpen: true,
            slots: [{ id: 1, open: '10:00 AM', close: '10:00 PM' }],
        },
    ],
    toggleDayOpen: (dayId) =>
        set((state) => ({
            timeSlotData: state.timeSlotData.map((day) =>
                day.id === dayId ? { ...day, isOpen: !day.isOpen } : day
            ),
        })),
    addSlot: (dayId, slot) =>
        set((state) => ({
            timeSlotData: state.timeSlotData.map((day) =>
                day.id === dayId ? { ...day, slots: [...day.slots, ...slot] } : day
            ),
        })),
    editSlot: (dayId, slotIndex, updatedSlot) =>
        set((state) => ({
            timeSlotData: state.timeSlotData.map((day) =>
                day.id === dayId
                    ? {
                          ...day,
                          slots: day.slots.map((s, i) => (i === slotIndex ? updatedSlot : s)),
                      }
                    : day
            ),
        })),
    deleteSlot: (dayId, slotIndex) =>
        set((state) => ({
            timeSlotData: state.timeSlotData.map((day) =>
                day.id === dayId
                    ? {
                          ...day,
                          slots: day.slots.filter((_, i) => i !== slotIndex),
                      }
                    : day
            ),
        })),
}));
