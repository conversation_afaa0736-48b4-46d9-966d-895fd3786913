import { create } from 'zustand';
import { Dayjs } from 'dayjs';

type DateRange = {
    start_date: Dayjs;
    end_date: Dayjs;
};

export interface InsightsState {
    dateRangeFilter: DateRange | null;
    setDateRangeFilter: (dateRangeFilter: DateRange | null) => void;
}

export const useInsightsStore = create<InsightsState>((set) => ({
    dateRangeFilter: null,
    setDateRangeFilter: (dateRangeFilter) => set({ dateRangeFilter }),
}));
