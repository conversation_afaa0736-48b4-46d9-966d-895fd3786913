import * as yup from 'yup';

export type AddItemValidationResolver = {
    item_img: string;
    category_name: string;
    sub_category_name: string;
    dish_type: string;
    dish_name: string;
    dish_desc: string;
    dish_price: string;
    packing_price: string;
    gst: string;
};

export const AddItemValidationSchema = yup.object().shape({
    item_img: yup.string().required('Item image is required'),
    category_name: yup.string().required('Category is required'),
    sub_category_name: yup.string().required('Sub Category is required'),
    dish_type: yup.string().required('Dish Type is required'),
    dish_name: yup
        .string()
        .required('Dish Name is required')
        .matches(/^[A-Za-z ]+$/, 'Dish Name can only contain letters and spaces')
        .min(3, 'Dish Name must be at least 3 characters')
        .max(35, 'Dish Name must be at maximum of 35 characters'),
    dish_desc: yup
        .string()
        .required('Dish Description is required')
        .min(10, 'Dish Description must be at least 10 characters')
        .max(300, 'Dish Description must be at most 300 characters')
        .matches(
            /^[\w\d\s.,'"\-()&!?]+$/,
            'Description can only contain letters, numbers, spaces, and basic punctuation'
        ),
    dish_price: yup
        .string()
        .required('Dish Price is required')
        .matches(/^[0-9]+$/, 'Dish Price can only contain digits'),
    packing_price: yup
        .string()
        .required('Packing Price is required')
        .matches(/^[0-9]+$/, 'Packing Price can only contain digits'),
    gst: yup.string().required('GST is required'),
});

export type CategoryNameResolver = {
    category_name: string;
};

export const categoryNameValidation = yup.object().shape({
    category_name: yup
        .string()
        .required('Category Name is required')
        .matches(/^[A-Za-z 0-9]+$/, 'Category Name can only contain letters, digits and spaces')
        .min(3, 'Category Name must be at least 3 characters')
        .max(35, 'Category Name must be at maximum of 35 characters'),
});

export type SubCategoryNameResolver = {
    category_name: string;
    sub_category_name: string;
};

export const subCategoryNameValidation = yup.object().shape({
    category_name: yup.string().required('Category is required'),
    sub_category_name: yup
        .string()
        .required('Sub Category Name is required')
        .matches(/^[A-Za-z 0-9]+$/, 'Sub Category Name can only contain letters, digits and spaces')
        .min(3, 'Sub Category Name must be at least 3 characters')
        .max(35, 'Sub Category Name must be at maximum of 35 characters'),
});
