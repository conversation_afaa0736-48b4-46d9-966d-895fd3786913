import { create } from 'zustand';
import { CategoryItem, SubCategoryItem } from '~/utils/mock';
import { ItemTabsType } from './itemTypes';

type AddIemType = 'category' | 'subcategory' | 'item';

type AddEditCategoryConfirmModeValue = {
    category_name: {
        new_name: string;
        old_name: string;
    };
    sub_category_name: {
        new_name: string;
        old_name: string;
    };
};

type ItemsState = {
    addItemEditMode: CategoryItem | SubCategoryItem | null;
    setAddItemEditMode: (val: CategoryItem | SubCategoryItem | null) => void;

    addItemType: AddIemType | null;
    setAddItemType: (val: AddIemType | null) => void;

    addEditCategoryConfirmModeValue: AddEditCategoryConfirmModeValue | null;
    setAddEditCategoryConfirmModeValue: (mode: AddEditCategoryConfirmModeValue | null) => void;

    addEditCategoryMode: 'add' | 'edit' | null;
    setAddEditCategoryMode: (mode: 'add' | 'edit' | null) => void;

    addItemMainImageUrl: string | null;
    setAddItemMainImageUrl: (url: string | null) => void;

    showSheetOptions: boolean;
    setShowSheetOptions: (val: boolean) => void;

    itemListHideType: 'category' | 'subcategory' | 'item' | null;
    setItemListHideType: (type: 'category' | 'subcategory' | 'item' | null) => void;

    itemListCategoryHidding: boolean;
    setItemListCategoryHidding: (val: boolean) => void;

    itemListCategoryOffline: 'enable' | 'disable' | null;
    setItemListCategoryOffline: (val: 'enable' | 'disable' | null) => void;

    categoryItemOnlineStatus: Record<string, boolean>;
    setCategoryItemOnlineStatus: (status: Record<string, boolean>) => void;

    hiddenCategories: Record<string, boolean>;
    setHiddenCategories: (status: Record<string, boolean>) => void;
};

export const useItemsStore = create<ItemsState>((set) => ({
    //menu - editor
    addItemEditMode: null,
    setAddItemEditMode: (val) => set({ addItemEditMode: val }),

    addItemType: null,
    setAddItemType: (val) => set({ addItemType: val }),

    addEditCategoryConfirmModeValue: null,
    setAddEditCategoryConfirmModeValue: (mode) => set({ addEditCategoryConfirmModeValue: mode }),

    addEditCategoryMode: null,
    setAddEditCategoryMode: (mode) => set({ addEditCategoryMode: mode }),

    addItemMainImageUrl: null,
    setAddItemMainImageUrl: (url) => set({ addItemMainImageUrl: url }),

    showSheetOptions: false,
    setShowSheetOptions: (val) => set({ showSheetOptions: val }),

    //item - list
    itemListHideType: null,
    setItemListHideType: (mode) => set({ itemListHideType: mode }),

    itemListCategoryHidding: false,
    setItemListCategoryHidding: (val) => set({ itemListCategoryHidding: val }),

    itemListCategoryOffline: null,
    setItemListCategoryOffline: (val) => set({ itemListCategoryOffline: val }),

    categoryItemOnlineStatus: {},
    setCategoryItemOnlineStatus: (status) => set({ categoryItemOnlineStatus: status }),

    hiddenCategories: {},
    setHiddenCategories: (status) => set({ hiddenCategories: status }),
}));
