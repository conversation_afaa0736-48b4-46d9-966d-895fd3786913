import axios from 'axios';
import { useCallback } from 'react';

type LocationType = {
    latitude: number;
    longitude: number;
};

type FetchRouteProps = {
    deliveryPartnerLocation: {
        latitude: number;
        longitude: number;
    };
    pickupLocation: {
        latitude: number;
        longitude: number;
    };
};

const googleMapsUrl = process.env.EXPO_PUBLIC_GOOGLE_MAP_API_KEY;

export const mapsApis = () => {
    const getDistance = useCallback(async (origin: LocationType, destination: LocationType) => {
        try {
            const response = await axios.get(
                'https://maps.googleapis.com/maps/api/distancematrix/json',
                {
                    params: {
                        origins: `${origin.latitude},${origin.longitude}`,
                        destinations: `${destination.latitude},${destination.longitude}`,
                        key: process.env.EXPO_PUBLIC_GOOGLE_MAP_API_KEY,
                    },
                }
            );

            const data = response.data;

            if (data.status === 'OK' && data.rows[0].elements[0].status === 'OK') {
                const distanceValue = data.rows[0].elements[0];
                return distanceValue;
            } else {
                return {};
            }
        } catch (error) {
            console.log('An error occurred while fetching distance : ', error);
            return {};
        }
    }, []);

    const fetchRoute = async ({ deliveryPartnerLocation, pickupLocation }: FetchRouteProps) => {
        const origin = `${deliveryPartnerLocation.latitude},${deliveryPartnerLocation.longitude}`;
        const destination = `${pickupLocation.latitude},${pickupLocation.longitude}`;
        const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${destination}&mode=driving&key=${googleMapsUrl}`;

        try {
            const response = await axios.get(url);
            const data = await response.data;

            if (data.routes.length > 0) {
                const points = data.routes[0].overview_polyline.points;
                const decodedRoute = decodePolyline(points);
                return decodedRoute;
            } else {
                return [];
            }
        } catch (error) {
            console.log('Error fetching directions : ', error);
            return [];
        }
    };

    const decodePolyline = (encoded: string) => {
        let polyline = [];
        let index = 0;
        let lat = 0;
        let lng = 0;

        while (index < encoded.length) {
            let b,
                shift = 0,
                result = 0;
            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);
            let dlat = result & 1 ? ~(result >> 1) : result >> 1;
            lat += dlat;

            shift = 0;
            result = 0;
            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);
            let dlng = result & 1 ? ~(result >> 1) : result >> 1;
            lng += dlng;

            polyline.push({
                latitude: lat / 1e5,
                longitude: lng / 1e5,
            });
        }

        return polyline;
    };

    const haversineDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
        const R = 6371000; // Radius of Earth in meters
        const dLat = ((lat2 - lat1) * Math.PI) / 180;
        const dLon = ((lon2 - lon1) * Math.PI) / 180;

        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos((lat1 * Math.PI) / 180) *
                Math.cos((lat2 * Math.PI) / 180) *
                Math.sin(dLon / 2) *
                Math.sin(dLon / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;

        return distance; // in meters
    };

    return { getDistance, fetchRoute, haversineDistance };
};
