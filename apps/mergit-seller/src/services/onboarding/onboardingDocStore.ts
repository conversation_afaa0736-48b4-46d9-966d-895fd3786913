import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { mmkvStorage } from '~/utils/storage';

export type OnboardingStep = 'aadhar' | 'pan' | 'gst' | 'fssai' | 'trade-license';

type DocSides = {
    front: string | null;
    back?: string | null;
};

type OnboardingDocState = {
    step: OnboardingStep;
    setStep: (step: OnboardingStep) => void;
    reset: () => void;

    images: Record<OnboardingStep, DocSides>;
    setImages: (from: OnboardingStep, side: keyof DocSides, image: string) => void;
};

export const useOnboardingDocStore = create<OnboardingDocState>()(
    persist(
        (set) => ({
            step: 'aadhar',
            setStep: (step) => set({ step }),
            reset: () => set({ step: 'aadhar' }),

            images: {
                aadhar: {
                    front: null,
                    back: null,
                },
                pan: {
                    front: null,
                },
                gst: {
                    front: null,
                },
                fssai: {
                    front: null,
                },
                'trade-license': {
                    front: null,
                },
            },
            setImages: (from, side, image) => {
                set((state) => ({
                    images: {
                        ...state.images,
                        [from]: {
                            ...state.images[from],
                            [side]: image,
                        },
                    },
                }));
            },
        }),
        {
            name: 'onboarding-docs',
            storage: createJSONStorage(() => mmkvStorage),
            partialize: (state) => ({ step: state.step }),
        }
    )
);
