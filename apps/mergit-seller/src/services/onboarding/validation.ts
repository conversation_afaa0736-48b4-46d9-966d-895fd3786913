import * as yup from 'yup';

export const personalInfoSchema = yup.object({
    mobile: yup
        .string()
        .required('Mobile Number is required')
        .matches(/^[0-9]{10}$/, 'Mobile Number must be exactly 10 digits'),

    email: yup
        .string()
        .required('Email is required')
        .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Invalid Email'),

    fullName: yup
        .string()
        .required('Owner Full Name is required')
        .min(3, 'Owner Full Name should be at least 3 characters')
        .matches(/^[A-Za-z ]+$/, 'Owner Full Name can only contain letters and spaces'),
});

export const bankDetailsSchema = yup.object().shape({
    bankName: yup.string().required('Bank Name is required'),
    accountHolderName: yup
        .string()
        .required('Account Holder Name is required')
        .min(3, 'Account Holder Name should be at least 3 characters')
        .max(50, 'Account Holder Name should not exceed 50 characters')
        .matches(/^[A-Za-z ]+$/, 'Account Holder Name can only contain letters and spaces'),
    accountNumber: yup
        .string()
        .required('Account Number is required')
        .matches(/^\d+$/, 'Account Number should contain only digits')
        .min(9, 'Account Number should be at least 9 digits')
        .max(18, 'Account Number should not exceed 18 digits'),
    ifscCode: yup
        .string()
        .required('IFSC Code is required')
        .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC Code'),
});

export const shopInfoSchema = yup.object().shape({
    name: yup.string().when('$isFood', {
        is: true,
        then: (schema) =>
            schema
                .required('Restaurant Name is required')
                .matches(
                    /^[A-Za-z0-9 ]+$/,
                    'Restaurant Name can only contain letters, digits and spaces'
                )
                .min(3, 'Restaurant Name must be at least 3 characters'),
        otherwise: (schema) =>
            schema
                .required('Shop Name is required')
                .matches(/^[A-Za-z0-9 ]+$/, 'Shop Name can only contain letters, digits and spaces')
                .min(3, 'Shop Name must be at least 3 characters'),
    }),
    restaurantType: yup.string().when('$isFood', {
        is: true,
        then: (schema) => schema.required('Restaurant type is required'),
        otherwise: (schema) => schema.notRequired(),
    }),
    image: yup.string().required('Shop Image is required'),
    logo: yup.string().required('Shop Logo is required'),
});

export const addressSchema = yup.object().shape({
    shopNo: yup
        .string()
        .required('Shop No / Building No is required')
        .matches(
            /^[A-Za-z0-9 .,/#-]+$/,
            'Shop No / Building No must not contain special characters except(/, -,.,, or #)'
        ),
    floor: yup
        .string()
        .required('Floor / Tower is required')
        .matches(
            /^[A-Za-z0-9 /-]+$/,
            'Floor / Tower must not contain special characters except(/ or -)'
        )
        .max(30, 'Floor / Tower should not exceed 30 characters'),
    area: yup
        .string()
        .required('Area / Locality is required')
        .matches(/^[A-Za-z0-9 ]+$/, 'Area / Locality must not contain special characters')
        .min(3, 'Area / Locality must be at least 3 characters')
        .max(50, 'Area / Locality should not exceed 30 characters'),
    city: yup.string().required('City is required'),
    landmark: yup.string().required('Landmark is required'),
});

export const aadharSchema = yup.object({
    aadharNumber: yup
        .string()
        .required('Aadhar Card Number is required')
        .matches(/^[0-9]+$/, 'Aadhar Card Number must contain only digits')
        .min(12, 'Aadhar Card Number must be exactly 12 digits')
        .max(12, 'Aadhar Card Number must be exactly 12 digits'),

    aadharFrontImageUri: yup.string().required('Aadhar Card Front image is required'),

    aadharBackImageUri: yup.string().required('Aadhar Card Back image is required'),
});

export const panSchema = yup.object({
    panNumber: yup
        .string()
        .required('PAN Card Number is required')
        .matches(
            /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
            'PAN Number must be in format : 5 letters + 4 digits + 1 letter'
        )
        .min(10, 'PAN Number must be exactly 10 characters')
        .max(10, 'PAN Number must be exactly 10 characters'),

    panFrontImageUri: yup.string().required('PAN Card Front image is required'),
});

export const gstSchema = yup.object({
    gstNumber: yup
        .string()
        .required('GST Number is required')
        .matches(
            /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[0-9A-Z]{1}Z[0-9A-Z]{1}$/,
            'GST Number must follow 15-character valid format'
        )
        .min(15, 'GST Number must be exactly 15 characters')
        .max(15, 'GST Number must be exactly 15 characters'),

    gstFrontImageUri: yup.string().required('GST Certificate is required'),
});

export const fssaiSchema = yup.object({
    fssaiNumber: yup
        .string()
        .required('FSSAI Number is required')
        .matches(/^\d{14}$/, 'FSSAI Number must be 14 digits')
        .min(14, 'FSSAI Number must be exactly 14 characters')
        .max(14, 'FSSAI Number must be exactly 14 characters'),

    fssaiExpiryDate: yup
        .string()
        .required('FSSAI Expiry Date is required')
        .matches(/^\d{2}\/\d{2}\/\d{4}$/, 'FSSAI Expiry Date must be in format : DD/MM/YYYY'),

    fssaiFrontImageUri: yup.string().required('FSSAI Certificate is required'),
});

export const tradeLicenseSchema = yup.object({
    tradeLicenseNumber: yup
        .string()
        .required('Trade License Number is required')
        .matches(
            /^[A-Z]{1,5}\/[A-Z0-9]{2,10}\/\d{4}\/\d{3,10}$|^\d{3,10}\/\d{4}$/,
            'Trade License Number format : TL/BBMP/2025/123456 or 123456/2025'
        ),

    tradeLicenseFrontImageUri: yup.string().required('Trade License Certificate is required'),
});
