import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { mmkvStorage } from 'utils/storage';

type StepStatus = 'active' | 'completed' | 'pending' | null;

interface Step {
    id: number;
    title: string;
    subtitle: string;
    status: StepStatus;
}

interface OnboardingState {
    steps: Step[];
    setActiveStep: (id: number) => void;
    completeStep: (id: number) => void;
    resetSteps: () => void;
}

const defaultSteps: Step[] = [
    {
        id: 1,
        title: 'Personal Information',
        subtitle: 'Seller Owner Information',
        status: null,
    },
    {
        id: 2,
        title: 'Shop Details',
        subtitle: 'Shop Location and Specifications',
        status: null,
    },
    {
        id: 3,
        title: 'Documents',
        subtitle: 'Shop Documents and Licenses',
        status: null,
    },
    {
        id: 4,
        title: 'Seller Partner Contract',
        subtitle: 'Privacy Policy & Onboarding Fee',
        status: null,
    },
];

export const useOnboardingStore = create<OnboardingState>()(
    persist(
        (set) => ({
            steps: defaultSteps,

            setActiveStep: (id) =>
                set((state) => ({
                    steps: state.steps.map((step) =>
                        step.id === id
                            ? { ...step, status: 'active' }
                            : step.status === 'active'
                              ? { ...step, status: null }
                              : step
                    ),
                })),

            completeStep: (id) =>
                set((state) => ({
                    steps: state.steps.map((step) =>
                        step.id === id ? { ...step, status: 'completed' } : step
                    ),
                })),

            resetSteps: () => set({ steps: defaultSteps }),
        }),
        {
            name: 'onboarding-steps',
            storage: createJSONStorage(() => mmkvStorage),
            partialize: (state) => ({ steps: state.steps }),
        }
    )
);
