export const formatTimeInput = (value: string) => {
    let val = value.replace(/[^0-9aApPmM]/g, '').toUpperCase();

    const hour = val.slice(0, 2);
    const minute = val.slice(2, 4);
    const meridiemRaw = val.slice(4, 6);

    let formatted = hour;
    if (minute) formatted += `:${minute}`;

    if (meridiemRaw) {
        if (meridiemRaw === 'AM' || meridiemRaw === 'PM') {
            formatted += ` ${meridiemRaw}`;
        } else if (meridiemRaw[0] === 'A') {
            formatted += ` A`;
        } else if (meridiemRaw[0] === 'P') {
            formatted += ` P`;
        }
    }

    return formatted.trim();
};
