import { create } from 'zustand';
import { Order } from './orderTypes';
import { orders } from '~/utils/mock';

type MoveOrderFrom = 'preparingOrders' | 'readyOrders' | 'newOrders';
type MoveOrderTo = 'preparingOrders' | 'readyOrders' | 'pickedUpOrders';

type RemoveOrderFrom = 'newOrders' | 'pickedUpOrders';

export interface OrderState {
    floatingCardShow: boolean;
    updateFloatingCardShow: (floatingCardShow: boolean) => void;

    selectedOrderId: string | null;
    setSelectedOrderId: (selectedOrderId: string | null) => void;

    isSpecifiOrderOpenTime: number | null;
    setIsSpecifiOrderOpenTime: (isSpecifiOrderOpenTime: number | null) => void;

    preparingOrders: Order[] | null;
    readyOrders: Order[] | null;
    pickedUpOrders: Order[] | null;
    newOrders: Order[] | null;

    moveOrder: (orderId: string, from: MoveOrder<PERSON>rom, target: MoveOrderTo) => void;
    removeOrder: (orderId: string, from: RemoveOrderFrom) => void;
}

export const useOrderStore = create<OrderState>((set) => ({
    floatingCardShow: false,
    updateFloatingCardShow: (floatingCardShow) => set({ floatingCardShow }),

    selectedOrderId: null,
    setSelectedOrderId: (selectedOrderId) => set({ selectedOrderId }),

    isSpecifiOrderOpenTime: null,
    setIsSpecifiOrderOpenTime: (isSpecifiOrderOpenTime) => set({ isSpecifiOrderOpenTime }),

    preparingOrders: null,
    readyOrders: null,
    pickedUpOrders: null,
    newOrders: orders,

    moveOrder: (orderId, from, target) =>
        set((state) => {
            const fromList = state[from];
            if (!fromList) return state;

            const idx = fromList.findIndex((o) => o.id === orderId);
            if (idx === -1) return state;

            const order = fromList[idx];

            const newFromList = [...fromList];
            newFromList.splice(idx, 1);

            const toList = state[target] ? [...(state[target] as Order[])] : [];
            toList.unshift(order);

            return {
                [from]: newFromList,
                [target]: toList,
            } as Partial<OrderState>;
        }),

    removeOrder: (orderId, from) =>
        set((state) => {
            const fromList = state[from];
            if (!fromList) return state;
            const idx = fromList.findIndex((o) => o.id === orderId);
            if (idx === -1) return state;
            const newList = [...fromList];
            newList.splice(idx, 1);
            return { [from]: newList } as Partial<OrderState>;
        }),
}));
