import * as yup from 'yup';

export type RequestItemValidationResolver = yup.InferType<typeof RequestItemValidationSchema>;

export const RequestItemValidationSchema = yup.object().shape({
    item_img: yup.string().required('Main image is required'),
    item_images: yup
        .array()
        .of(
            yup.object({
                uri: yup.string().required(),
                height: yup.number().required(),
                width: yup.number().required(),
            })
        )
        .required('Gallery images are required')
        .max(5, 'Maximum 5 images are allowed'),
    category_name: yup.string().required('Category is required'),
    sub_category_name: yup.string().required('Sub Category is required'),
    item_type: yup
        .string()
        .required()
        .when('$from', {
            is: 'product',
            then: (schema) => schema.required('Product Type is required'),
            otherwise: (schema) => schema.required('Item Type is required'),
        }),
    item_name: yup
        .string()
        .required()
        .when('$from', {
            is: 'product',
            then: (schema) =>
                schema
                    .required('Product Name is required')
                    .matches(/^[A-Za-z ]+$/, 'Product Name can only contain letters and spaces')
                    .min(3, 'Product Name must be at least 3 characters')
                    .max(35, 'Product Name must be at maximum of 35 characters'),
            otherwise: (schema) =>
                schema
                    .required('Item Name is required')
                    .matches(/^[A-Za-z ]+$/, 'Item Name can only contain letters and spaces')
                    .min(3, 'Item Name must be at least 3 characters')
                    .max(35, 'Item Name must be at maximum of 35 characters'),
        }),
    item_desc: yup
        .string()
        .required()
        .when('$from', {
            is: 'product',
            then: (schema) =>
                schema
                    .required('Product Description is required')
                    .min(10, 'Product Description must be at least 10 characters')
                    .max(300, 'Product Description must be at most 300 characters')
                    .matches(
                        /^[\w\d\s.,'"\-()&!?]+$/,
                        'Product Description can only contain letters, numbers, spaces, and basic punctuation'
                    ),
            otherwise: (schema) =>
                schema
                    .required('Item Description is required')
                    .min(10, 'Item Description must be at least 10 characters')
                    .max(300, 'Item Description must be at most 300 characters')
                    .matches(
                        /^[\w\d\s.,'"\-()&!?]+$/,
                        'Item Description can only contain letters, numbers, spaces, and basic punctuation'
                    ),
        }),
    item_price: yup
        .string()
        .required()
        .when('$from', {
            is: 'product',
            then: (schema) =>
                schema
                    .required('Product Price is required')
                    .matches(/^[0-9]+$/, 'Product Price can only contain digits'),
            otherwise: (schema) =>
                schema
                    .required('Item Price is required')
                    .matches(/^[0-9]+$/, 'Item Price can only contain digits'),
        }),
    packing_price: yup
        .string()
        .required('Packing Price is required')
        .matches(/^[0-9]+$/, 'Packing Price can only contain digits'),
    gst: yup.string().required('GST is required'),
});
