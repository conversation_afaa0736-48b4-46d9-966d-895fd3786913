import { create } from 'zustand';

type ImageFormatProps = {
    uri: string;
    height: number;
    width: number;
};

type GroceryState = {
    addItemMainImageUrl: ImageFormatProps | null;
    setAddItemMainImageUrl: (url: ImageFormatProps | null) => void;

    addItemImages: ImageFormatProps[] | null;
    setAddItemImages: (images: ImageFormatProps[] | null) => void;

    itemListHideType: 'category' | 'subcategory' | 'item' | null;
    setItemListHideType: (type: 'category' | 'subcategory' | 'item' | null) => void;

    itemListCategoryHidding: boolean;
    setItemListCategoryHidding: (val: boolean) => void;

    itemListCategoryOffline: 'enable' | 'disable' | null;
    setItemListCategoryOffline: (val: 'enable' | 'disable' | null) => void;

    categoryItemOnlineStatus: Record<string, boolean>;
    setCategoryItemOnlineStatus: (status: Record<string, boolean>) => void;

    hiddenCategories: Record<string, boolean>;
    setHiddenCategories: (status: Record<string, boolean>) => void;
};

export const useGroceryStore = create<GroceryState>((set) => ({
    // request-item
    addItemMainImageUrl: null,
    setAddItemMainImageUrl: (url) => set({ addItemMainImageUrl: url }),

    addItemImages: null,
    setAddItemImages: (images) => set({ addItemImages: images }),

    // item-list
    categoryItemOnlineStatus: {},
    setCategoryItemOnlineStatus: (status) => set({ categoryItemOnlineStatus: status }),

    hiddenCategories: {},
    setHiddenCategories: (status) => set({ hiddenCategories: status }),

    itemListHideType: null,
    setItemListHideType: (mode) => set({ itemListHideType: mode }),

    itemListCategoryHidding: false,
    setItemListCategoryHidding: (val) => set({ itemListCategoryHidding: val }),

    itemListCategoryOffline: null,
    setItemListCategoryOffline: (val) => set({ itemListCategoryOffline: val }),
}));
