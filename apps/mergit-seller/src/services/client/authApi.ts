import { useMutation } from '@tanstack/react-query';
import { axiosInstance } from '~/helpers';

export const useAuthApi = () => {
    const registerSeller = useMutation({
        mutationFn: async (identifier: string) => {
            const response = await axiosInstance.post(`/auth/v1/auth-service/login`, {
                identifier,
            });
            return response.data;
        },
    });

    const verifyOtp = useMutation({
        mutationFn: async ({ identifier, otp }: { identifier: string; otp: string }) => {
            const response = await axiosInstance.put(`/auth/v1/auth-service/seller-verified-otp`, {
                identifier,
                otp,
            });
            return response.data;
        },
    });

    const sellerLogout = useMutation({
        mutationFn: async () => {
            const response = await axiosInstance.post(`/auth/v1/auth-service/logout`);
            return response.data;
        },
    });

    return {
        registerSeller,
        verifyOtp,
        sellerLogout,
    };
};
