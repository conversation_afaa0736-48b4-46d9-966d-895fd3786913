import * as yup from 'yup';

export const signInSchema = yup.object({
    'mobile-email': yup
        .string()
        .required('Mobile number or Email is required')
        .test('is-valid-mobile-or-email', 'Enter a valid Mobile Number or Email', (value) => {
            if (!value) return false;

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const mobileRegex = /^\d{10}$/;

            return emailRegex.test(value) || mobileRegex.test(value);
        }),
});

export type SignUpValidationResolver = {
    'mobile-email': string;
    agent: string;
    agentCode: string | undefined;
};

export const signUpSchema = yup.object({
    'mobile-email': yup
        .string()
        .required('Mobile number or Email is required')
        .test('is-valid-mobile-or-email', 'Enter a valid Mobile Number or Email', (value) => {
            if (!value) return false;

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const mobileRegex = /^\d{10}$/;

            return emailRegex.test(value) || mobileRegex.test(value);
        }),
    agent: yup.string().required('*Agent is required'),
    agentCode: yup.string().when('agent', {
        is: (val: string) => val === 'yes',
        then: (schema) => schema.required('Agent Id is required'),
        otherwise: (schema) => schema.optional(),
    }),
});

export const gstSchema = yup.object({
    gstType: yup.string().required('GST Type is required'),
    gstNumber: yup
        .string()
        .when('gstType', {
            is: (val: string) => val !== 'no-gst',
            then: (schema) => schema.required('GST Number is required'),
            otherwise: (schema) => schema.optional(),
        })
        .matches(
            /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[0-9A-Z]{1}Z[0-9A-Z]{1}$/,
            'GST Number must follow 15-character valid format'
        )
        .min(15, 'GST Number must be exactly 15 characters')
        .max(15, 'GST Number must be exactly 15 characters'),
});
