export const isEmail = (input: string): boolean => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);

export const maskMobileOrEmail = (input: string): string => {
    if (!input) return '';

    const mobileRegex = /^\d{10}$/;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (mobileRegex.test(input)) {
        const part1 = input.slice(0, 5);
        const part2 = input.slice(5, 6);
        return `+91 ${part1} ${part2}XXXX`;
    }

    if (emailRegex.test(input)) {
        const [username, domain] = input.split('@');
        const visibleChars = username.slice(0, 2);
        const hidden = '*'.repeat(Math.max(2, username.length - 2));
        return `${visibleChars}${hidden}@${domain}`;
    }

    return input;
};
