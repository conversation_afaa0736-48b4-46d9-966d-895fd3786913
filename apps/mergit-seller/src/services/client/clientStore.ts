import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { mmkvStorage } from '~/utils/storage';

export type GstType = 'regular-gst' | 'composite-gst' | 'no-gst';

interface ServiceType {
    id: number;
    label: string;
    value: string;
}

interface ClientState {
    token: string | null;
    login: (token: string) => void;
    logout: () => void;

    authToken: string | null;
    setAuthToken: (authToken: string) => void;
    removeAuthToken: () => void;

    refreshToken: string | null;
    setRefreshToken: (refreshToken: string) => void;
    removeRefreshToken: () => void;

    gstType: null | GstType;
    setGstType: (val: GstType | null) => void;

    isServiceTypeSelected: boolean;
    setServiceTypeSelected: (val: boolean) => void;

    isOnboardingCompleted: boolean;
    completeOnboarding: (val: boolean) => void;

    forDocOnboarding: boolean;
    setForDocOnboarding: (val: boolean) => void;

    selectedService: ServiceType | null;
    setSelectedService: (service: ServiceType) => void;
    resetService: () => void;
}

export const useClientStore = create<ClientState>()(
    persist(
        (set) => ({
            token: null,
            login: (token) => set({ token }),
            logout: () => set({ token: null }),

            authToken: null,
            setAuthToken: (authToken) => set({ authToken }),
            removeAuthToken: () => set({ authToken: null }),

            refreshToken: null,
            setRefreshToken: (refreshToken) => set({ refreshToken }),
            removeRefreshToken: () => set({ refreshToken: null }),

            gstType: null,
            setGstType: (val) => set({ gstType: val }),

            isServiceTypeSelected: false,
            setServiceTypeSelected: (val) => set({ isServiceTypeSelected: val }),

            isOnboardingCompleted: false,
            completeOnboarding: (val) => set({ isOnboardingCompleted: val }),

            forDocOnboarding: false,
            setForDocOnboarding: (val) => set({ forDocOnboarding: val }),

            selectedService: {
                id: 1,
                label: 'Food',
                value: 'food',
            },
            setSelectedService: (service) => {
                const selectedService = {
                    id: service.id,
                    label: service.label,
                    value: service.value,
                };
                set({ selectedService });
            },
            resetService: () => set({ selectedService: null }),
        }),
        {
            name: 'auth-store',
            storage: createJSONStorage(() => mmkvStorage),
            partialize: (state) => ({
                token: state.token,
                authToken: state.authToken,
                gstType: state.gstType,
                isServiceTypeSelected: state.isServiceTypeSelected,
                isOnboardingCompleted: state.isOnboardingCompleted,
                selectedService: state.selectedService,
            }),
        }
    )
);
