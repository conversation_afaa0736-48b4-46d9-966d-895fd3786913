import { create } from 'zustand';

type OutletState = {
    isOnline: boolean;
    setOnline: (val: boolean) => void;
    toggleOnline: () => void;

    shopName: string;
    setShopName: (val: string) => void;

    shopLogo: string;
    setShopLogo: (val: string) => void;
};

export const useOutletStore = create<OutletState>((set) => ({
    isOnline: false,
    setOnline: (val) => set({ isOnline: val }),
    toggleOnline: () => set((state) => ({ isOnline: !state.isOnline })),

    shopName: 'SS Hyderabad Biriyani',
    setShopName: (val) => set({ shopName: val }),

    shopLogo:
        'https://fastly.picsum.photos/id/23/3887/4899.jpg?hmac=2fo1Y0AgEkeL2juaEBqKPbnEKm_5Mp0M2nuaVERE6eE',
    setShopLogo: (val) => set({ shopLogo: val }),
}));
