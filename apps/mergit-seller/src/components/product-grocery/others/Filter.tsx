import {
    View,
    Text,
    TouchableOpacity,
    Animated,
    Pressable,
} from 'react-native';
import Types from './Types';
import { CustomBtn, useBottomSheet } from '@mergit-mobile/components';
import { fonts } from '@mergit-mobile/styles';
import { useMemo, useRef, useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useTabsStore } from '~/services/tabs';

const CATEGORIES = ['Type'];
const ANIMATION_DURATION = 250;

interface FilterProps {
    forService: 'grocery' | 'product';
}

const Filter = ({ forService }: FilterProps) => {
    const { hideBottomSheet } = useBottomSheet();
    const { bottomInset } = useTabsStore();

    const categoryPositions = useRef<Record<string, number>>({}).current;
    const categoryHeights = useRef<Record<string, number>>({}).current;

    const currentIndicatorY = useRef(0);
    const contentTranslateX = useRef(new Animated.Value(0)).current;
    const indicatorAnim = useRef(new Animated.Value(0)).current;

    const [selectedCategory, setSelectedCategory] = useState(CATEGORIES[0]);

    const measureCategoryPosition = (category: string, event: any) => {
        const { y, height } = event.nativeEvent.layout;
        categoryPositions[category] = y;
        categoryHeights[category] = height;

        if (selectedCategory === category && currentIndicatorY.current === 0) {
            indicatorAnim.setValue(y);
            currentIndicatorY.current = y;
        }
    };

    const handleCategorySelect = (category: string) => {
        if (categoryPositions[category] !== undefined) {
            Animated.spring(indicatorAnim, {
                toValue: categoryPositions[category],
                damping: 15,
                stiffness: 100,
                useNativeDriver: true,
            }).start();
        }
        currentIndicatorY.current = categoryPositions[category];

        Animated.sequence([
            Animated.timing(contentTranslateX, {
                toValue: 30,
                duration: ANIMATION_DURATION,
                useNativeDriver: true,
            }),
            Animated.timing(contentTranslateX, {
                toValue: 0,
                duration: ANIMATION_DURATION,
                useNativeDriver: true,
            }),
        ]).start();

        setSelectedCategory(category);
    };

    const renderContent = useMemo(() => {
        if (selectedCategory === 'Type') {
            return <Types forService={forService} />;
        }
        return null;
    }, [selectedCategory]);

    const handleClearAll = () => {
        hideBottomSheet();
    };

    const handleApply = () => {
        hideBottomSheet();
    };

    return (
        <View className="h-full">
            <View className="flex-row items-center p-primary">
                <Text
                    style={fonts.fontSemiBold}
                    className="flex-1 text-text17 tracking-wide text-secondary">
                    Filter
                </Text>
                <Pressable hitSlop={10} onPress={hideBottomSheet}>
                    <Ionicons name="close" size={24} color="black" />
                </Pressable>
            </View>
            <View className="h-px bg-border" />
            <View className="flex-1 flex-row pl-primary">
                <View className="w-[40%] gap-2 border-r border-border bg-white py-4">
                    {CATEGORIES.map((category) => (
                        <TouchableOpacity
                            key={category}
                            activeOpacity={0.8}
                            onLayout={(e) => measureCategoryPosition(category, e)}
                            onPress={() => handleCategorySelect(category)}
                            style={{ paddingVertical: 12 }}>
                            <Text className="text-text16 text-secondary" style={fonts.fontRegular}>
                                {category}
                            </Text>
                        </TouchableOpacity>
                    ))}
                    <Animated.View
                        style={{
                            position: 'absolute',
                            right: 0,
                            width: 4,
                            height: categoryHeights[selectedCategory] ?? 40,
                            backgroundColor: '#1F7575',
                            borderTopLeftRadius: 10,
                            borderBottomLeftRadius: 10,
                            transform: [{ translateY: indicatorAnim }],
                        }}
                    />
                </View>

                <Animated.View
                    style={{
                        flex: 1,
                        transform: [{ translateX: contentTranslateX }],
                    }}>
                    {renderContent}
                </Animated.View>
            </View>

            <View
                className="w-full flex-row items-center gap-4 border-t border-t-border bg-white p-primary"
                style={{ paddingBottom: bottomInset }}>
                <CustomBtn
                    onPress={handleClearAll}
                    title="Clear All"
                    borderColor="#1F7575"
                    btnStyle={{ flex: 1 }}
                    backgroundColor="transparent"
                    textStyle={{ color: '#1F7575' }}
                />
                <CustomBtn onPress={handleApply} title="Apply" btnStyle={{ flex: 1 }} />
            </View>
        </View>
    );
};

export default Filter;
