import { View } from 'react-native';
import { Badges } from '~/components/common/Badges';
import { NavigationHelpers, TabNavigationState, ParamListBase } from '@react-navigation/native';
import { BottomTabNavigationEventMap } from '@react-navigation/bottom-tabs';

type MyTabBarProps = {
    state: TabNavigationState<ParamListBase>;
    descriptors: any;
    navigation: NavigationHelpers<ParamListBase, BottomTabNavigationEventMap>;
    from?: 'orders';
};

export function TopTabs({ state, descriptors, navigation, from }: MyTabBarProps) {
    const tabs = state.routes.map((route) => {
        const { options } = descriptors[route.key];
        return (options.tabBarLabel ?? options.title ?? route.name) as string;
    });

    const activeTab = tabs[state.index];

    const handleSelect = (tab: string) => {
        const targetIndex = tabs.indexOf(tab);
        const route = state.routes[targetIndex];

        const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
        });

        if (!event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
        }
    };

    return (
        <View className="w-full bg-background p-primary">
            <Badges
                tabs={tabs}
                activeTab={activeTab}
                onSelect={handleSelect}
                isShadow
                showLength={from === 'orders'}
            />
        </View>
    );
}
