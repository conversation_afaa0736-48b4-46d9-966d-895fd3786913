import { View } from 'react-native';
import { Image } from 'expo-image';
import { useState } from 'react';

type StarRatingsProps = {
    initial: number;
    starSize?: number;
    count?: number;
    fillColor?: string;
};

const StarRatings = (props: StarRatingsProps) => {
    const { initial = 0, starSize = 35, count = 5, fillColor = '#FEC84B' } = props;

    const [width, setWidth] = useState(0);

    return (
        <View
            onLayout={({ nativeEvent }) => setWidth(nativeEvent.layout.width)}
            className="overflow-hidden">
            <View className="flex-row items-center">
                {[...Array(count)].map((_, i) => (
                    <Image
                        key={`empty-${i}`}
                        source={require('assets/images/insights/ratings.png')}
                        style={{ width: starSize, height: starSize, marginLeft: -1.6 }}
                        contentFit="cover"
                    />
                ))}
            </View>

            {width > 0 && (
                <View
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: width * (initial / count),
                        height: '100%',
                        overflow: 'hidden',
                        backgroundColor: fillColor,
                    }}>
                    <View className="flex-row items-center">
                        {[...Array(count)].map((_, i) => (
                            <Image
                                key={`filled-${i}`}
                                source={require('assets/images/insights/ratings.png')}
                                style={{ width: starSize, height: starSize, marginLeft: -1.5 }}
                                contentFit="cover"
                            />
                        ))}
                    </View>
                </View>
            )}
        </View>
    );
};

export default StarRatings;
