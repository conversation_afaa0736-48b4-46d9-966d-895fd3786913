import { Feather } from '@expo/vector-icons';
import { fonts } from '@mergit-mobile/styles';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTabsStore } from '~/services/tabs';

interface ImgPickerTypeProps {
    openCamera: () => void;
    openFilePicker: () => void;
}

const ImgPickerType = ({ openCamera, openFilePicker }: ImgPickerTypeProps) => {
    const { bottomInset } = useTabsStore();
    return (
        <View className="px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
            <Text
                className="mb-4 text-text16 tracking-wide text-secondary"
                style={fonts.fontSemiBold}>
                Select Type
            </Text>
            <View className="flex-row items-center gap-4">
                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={openCamera}
                    className="h-[120px] flex-1 items-center justify-center rounded-xl border border-border">
                    <Feather name="camera" size={35} color="#1F7575" />
                    <Text
                        className="mt-4 text-center text-text15 text-secondary"
                        style={fonts.fontMedium}>
                        Camera
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={openFilePicker}
                    className="h-[120px] flex-1 items-center justify-center rounded-xl border border-border">
                    <Feather name="file-text" size={35} color="#1F7575" />
                    <Text
                        className="mt-4 text-center text-text15 text-secondary"
                        style={fonts.fontMedium}>
                        Files
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default ImgPickerType;
