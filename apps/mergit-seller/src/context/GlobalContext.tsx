import { createContext, useContext, useEffect, useState } from 'react';
import { Keyboard, Modal, StatusBar, View } from 'react-native';
import LottieView from 'lottie-react-native';

type GlobalContextValue = {
    setLoading: (val: boolean) => void;
};

export const GlobalContext = createContext<GlobalContextValue>({
    setLoading: () => {},
});

export const useGlobalContext = () => useContext(GlobalContext);

export const GlobalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (loading) {
            Keyboard.dismiss();
            StatusBar.setBarStyle('light-content');
        } else {
            StatusBar.setBarStyle('dark-content');
        }
    }, [loading]);

    return (
        <GlobalContext.Provider value={{ setLoading }}>
            {children}
            <Modal visible={loading} transparent statusBarTranslucent navigationBarTranslucent>
                <View className="flex-1 items-center justify-center bg-black/20">
                    <LottieView
                        source={require('assets/loader/loader.json')}
                        style={{ width: 250, height: 250 }}
                        autoPlay
                        loop
                    />
                </View>
            </Modal>
        </GlobalContext.Provider>
    );
};
