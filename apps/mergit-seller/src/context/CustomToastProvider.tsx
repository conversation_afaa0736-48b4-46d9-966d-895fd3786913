import React, { useEffect, useState } from 'react';
import { Text, ViewStyle } from 'react-native';
import Animated, {
    Easing,
    useSharedValue,
    withTiming,
    useAnimatedStyle,
    runOnJS,
} from 'react-native-reanimated';
import { fonts } from '@mergit-mobile/styles';
import { Image } from 'expo-image';

export type ToastPosition = 'top' | 'bottom' | 'center';
export type AnimationType = 'fade' | 'slide' | 'slideLeft' | 'slideRight';

interface ToastProps {
    message: string;
    position?: ToastPosition;
    duration?: number;
    animation?: AnimationType;
    onClose?: () => void;
}

interface ToastComponent extends React.FC<ToastProps> {
    show: (props: ToastProps) => void;
}

const Toast: ToastComponent = ({
    message,
    position = 'bottom',
    duration = 3000,
    animation = 'fade',
    onClose,
}) => {
    const opacity = useSharedValue(0);
    const translateY = useSharedValue(0);
    const translateX = useSharedValue(0);

    useEffect(() => {
        if (animation === 'slide') {
            translateY.value = position === 'bottom' ? 50 : -50;
        } else if (animation === 'slideLeft') {
            translateX.value = -200;
        } else if (animation === 'slideRight') {
            translateX.value = 200;
        }

        opacity.value = withTiming(1, {
            duration: 300,
            easing: Easing.inOut(Easing.ease),
        });
        translateY.value = withTiming(0, { duration: 300 });
        translateX.value = withTiming(0, { duration: 300 });

        const timer = setTimeout(() => {
            opacity.value = withTiming(
                0,
                {
                    duration: 300,
                },
                () => {
                    if (onClose) runOnJS(onClose)();
                }
            );

            if (animation === 'slide') {
                translateY.value = withTiming(position === 'bottom' ? 50 : -50, { duration: 300 });
            } else if (animation === 'slideLeft') {
                translateX.value = withTiming(-200, { duration: 300 });
            } else if (animation === 'slideRight') {
                translateX.value = withTiming(200, { duration: 300 });
            }
        }, duration);

        return () => clearTimeout(timer);
    }, [animation, duration, position]);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            opacity: opacity.value,
            transform:
                animation === 'slide'
                    ? [{ translateY: translateY.value }]
                    : animation === 'slideLeft' || animation === 'slideRight'
                      ? [{ translateX: translateX.value }]
                      : [],
        };
    });

    const positionStyle: ViewStyle =
        position === 'top' ? { top: 50 } : position === 'bottom' ? { bottom: 30 } : { top: '50%' };

    return (
        <Animated.View
            style={[
                {
                    position: 'absolute',
                    alignSelf: 'center',
                    padding: 8,
                    paddingRight: 15,
                    borderRadius: 100,
                    flexDirection: 'row',
                    alignItems: 'center',
                    maxWidth: '70%',
                    backgroundColor: '#545153',
                    zIndex: 9999,
                    gap: 6,
                    ...positionStyle,
                },
                animatedStyle,
            ]}>
            <Image
                source={require('assets/images/splash.png')}
                style={{ width: 32, height: 32, borderRadius: 100 }}
                contentFit="cover"
            />
            <Text
                style={{
                    fontSize: 14,
                    letterSpacing: 0.4,
                    ...fonts.fontRegular,
                    color: '#fff',
                }}>
                {message}
            </Text>
        </Animated.View>
    );
};

Toast.show = (props: ToastProps) => {
    const toastManager = ToastManager.getInstance();
    toastManager.show(props);
};

class ToastManager {
    private static instance: ToastManager | null = null;
    private setToastProps: ((props: ToastProps) => void) | null = null;

    private constructor() {}

    public static getInstance(): ToastManager {
        if (!ToastManager.instance) {
            ToastManager.instance = new ToastManager();
        }
        return ToastManager.instance;
    }

    public register(setToastProps: (props: ToastProps) => void) {
        this.setToastProps = setToastProps;
    }

    public show(props: ToastProps) {
        if (this.setToastProps) {
            this.setToastProps(props);
        }
    }
}

export const CustomToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [toastProps, setToastProps] = useState<ToastProps | null>(null);

    useEffect(() => {
        const toastManager = ToastManager.getInstance();
        toastManager.register(setToastProps);
    }, []);

    return (
        <>
            {children}
            {toastProps && <Toast {...toastProps} onClose={() => setToastProps(null)} />}
        </>
    );
};

export { Toast };
