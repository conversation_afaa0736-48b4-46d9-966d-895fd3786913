import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { fonts } from '@mergit-mobile/styles';
import { colors } from '~/utils/constants';
import { router } from 'expo-router';
import { PayoutCard } from '~/components/more/payout/PayoutCard';
import { PayoutBrackDownCard } from '~/components/more/payout/PayoutBrackDownCard';
import { useTabsStore } from '~/services/tabs';
import { useOrderStore } from '~/services/orders';

const PayoutDetail = () => {
    const { data } = useLocalSearchParams();
    const payoutData = JSON.parse(data as string);
    const { bottomInset } = useTabsStore();
    const { floatingCardShow } = useOrderStore();

    return (
        <ScrollView
            contentContainerClassName="flex-grow gap-4 px-primary"
            contentContainerStyle={{
                paddingBottom: floatingCardShow ? bottomInset + 75 : bottomInset,
            }}
            showsVerticalScrollIndicator={false}>
            <View className="flex-1 gap-6 bg-background pt-5">
                <View className="flex-row items-center gap-3">
                    <TouchableOpacity activeOpacity={0.8} onPress={() => router.back()}>
                        <Feather name="arrow-left" size={20} color={colors.secondary} />
                    </TouchableOpacity>
                    <Text
                        style={fonts.fontSemiBold}
                        className="flex-1 text-text16 tracking-wider text-secondary">
                        Payout Cycle ({payoutData?.payoutCycle})
                    </Text>
                </View>

                <PayoutCard data={payoutData} fromDetail />

                <Text
                    style={fonts.fontSemiBold}
                    className="text-text16 tracking-wide text-secondary">
                    Settlement Breakdown
                </Text>

                <PayoutBrackDownCard data={payoutData} />
            </View>
        </ScrollView>
    );
};

export default PayoutDetail;
