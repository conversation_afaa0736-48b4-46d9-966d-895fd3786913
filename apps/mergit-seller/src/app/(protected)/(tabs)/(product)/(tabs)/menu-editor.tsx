import { FlatList, Text, Pressable, Platform, View, Keyboard } from 'react-native';
import { sampleProducts, subCategoryData } from 'utils/mock';
import { Enty<PERSON>, Feather } from '@expo/vector-icons';
import { fonts } from '@mergit-mobile/styles';
import { router } from 'expo-router';
import { SearchCard } from '~/components/search';
import { colors } from '~/utils/constants';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useEffect, useState } from 'react';
import { CustomBtn, useBottomSheet } from '@mergit-mobile/components';
import { BulkUploadSheet, SuccessSheet } from '~/components/product-grocery/others';
import { useOrderStore } from '~/services/orders';

const MenuEditor = () => {
    const filteredProducts = sampleProducts.map((item) => ({
        ...item,
        count: subCategoryData[item.id as keyof typeof subCategoryData]?.length ?? 0,
    }));

    const { showBottomSheet, hideBottomSheet } = useBottomSheet();
    const { floatingCardShow } = useOrderStore();

    const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

    useEffect(() => {
        const show = Keyboard.addListener('keyboardDidShow', () => {
            setIsKeyboardVisible(true);
        });
        const hide = Keyboard.addListener('keyboardDidHide', () => {
            setIsKeyboardVisible(false);
        });

        return () => {
            show.remove();
            hide.remove();
        };
    }, []);

    const bulkUploadSheet = () => {
        showBottomSheet(
            () => <BulkUploadSheet onClose={hideBottomSheet} showSuccess={successSheet} />,
            'hide'
        );
    };

    const successSheet = () => {
        showBottomSheet(() => <SuccessSheet />, 'hide');
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}>
            <View className="flex-1 bg-background">
                <FlatList
                    data={filteredProducts}
                    keyExtractor={(item) => item.id}
                    contentContainerClassName="flex-grow gap-4 px-primary"
                    contentContainerStyle={{
                        paddingBottom: isKeyboardVisible ? 180 : floatingCardShow ? 150 : 75,
                    }}
                    ListHeaderComponent={
                        <View>
                            <View className="flex-row items-center gap-4 pb-primary">
                                <Text
                                    style={fonts.fontSemiBold}
                                    numberOfLines={1}
                                    className="flex-1 text-text17 tracking-wide text-secondary">
                                    Category
                                </Text>
                                <CustomBtn
                                    title="Bulk Upload"
                                    onPress={bulkUploadSheet}
                                    borderColor={colors.primary}
                                    backgroundColor={colors.background}
                                    textStyle={{ color: colors.primary }}
                                    icon={
                                        <Feather name="upload" size={18} color={colors.primary} />
                                    }
                                    height={45}
                                    isShadow
                                />
                            </View>
                            <SearchCard placeholder="Search by category name" />
                        </View>
                    }
                    renderItem={({ item }) => (
                        <Pressable
                            className="flex-row items-center gap-4 rounded-lg border border-border bg-white p-primary"
                            onPress={() =>
                                router.push({
                                    pathname: '/(protected)/(tabs)/(product)/sub-category',
                                    params: { id: item.id },
                                })
                            }>
                            <Text
                                style={fonts.fontMedium}
                                className="flex-1 text-text15 tracking-wider text-secondary">
                                {item.name} ({item.count})
                            </Text>
                            <Entypo name="chevron-small-right" size={22} color={colors.secondary} />
                        </Pressable>
                    )}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                />
                <View
                    className="absolute right-5 w-full items-end"
                    style={{ bottom: floatingCardShow ? 85 : 16 }}>
                    <CustomBtn
                        title="Request Items"
                        onPress={() =>
                            router.push({
                                pathname: '/(protected)/(request-item)',
                                params: { from: 'grocery' },
                            })
                        }
                        height={45}
                        icon={<Entypo name="plus" size={20} color="#fff" />}
                        isShadow
                    />
                </View>
            </View>
        </KeyboardAvoidingView>
    );
};

export default MenuEditor;
