import { View, Text, FlatList, Dimensions } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { Entypo } from '@expo/vector-icons';
import { router } from 'expo-router';
import { CustomBtn, useBottomSheet, useCustomModal } from '@mergit-mobile/components';
import {
    ActivateOfferSheet,
    ActiveOfferModel,
    OfferApplicable,
    Offercard,
    OffercardProps,
    OfferSucces,
    OtpVerificationSheet,
} from '~/components/offers';
import { offerData } from '~/utils/mock';
import { useOrderStore } from '~/services/orders';

const { width } = Dimensions.get('window');
const isMdScreen = width >= 768;

const Offer = () => {
    const { showBottomSheet, hideBottomSheet } = useBottomSheet();
    const { showModal } = useCustomModal();
    const { floatingCardShow } = useOrderStore();

    const activateOffer = (offer: OffercardProps) => {
        showBottomSheet(() => (
            <ActivateOfferSheet
                offer={offer}
                onClose={hideBottomSheet}
                onOfferApplicable={() => offerApplicable(offer)}
            />
        ));
    };

    const offerApplicable = (offer: OffercardProps) => {
        showBottomSheet(() => (
            <OfferApplicable
                offer={offer}
                onClose={hideBottomSheet}
                onApplayOffer={() => handleToggleStatus(offer)}
            />
        ));
    };

    const handleSuccessModel = (offer: any) => {
        showModal(<OfferSucces offer={offer} />);
    };

    const handleOtpBottomSheet = (offer: any) => {
        showBottomSheet(() => <OtpVerificationSheet onPress={() => handleSuccessModel(offer)} />);
    };

    const handleToggleStatus = (offer: any) => {
        showModal(<ActiveOfferModel offer={offer} onProceed={() => handleOtpBottomSheet(offer)} />);
    };

    return (
        <FlatList
            data={offerData as OffercardProps[]}
            numColumns={isMdScreen ? 2 : 1}
            keyExtractor={(item) => item?.id}
            ListHeaderComponent={() => (
                <View className="flex-row items-center gap-4">
                    <Text
                        style={fonts.fontSemiBold}
                        className="flex-1 text-text16 tracking-wide text-secondary">
                        Recommended offers
                    </Text>
                    <CustomBtn
                        title="Create Offers"
                        onPress={() => router.push('/(protected)/(create-offer)')}
                        height={42}
                        icon={<Entypo name="plus" size={20} color="#fff" />}
                        position="right"
                    />
                </View>
            )}
            renderItem={({ item }) => (
                <View className="w-full md:w-[49%]">
                    <Offercard
                        id={item.id}
                        offer={item.offer}
                        minOrder={item.minOrder}
                        maxDiscount={item.maxDiscount}
                        onActivateOffer={() => activateOffer(item)}
                        isActive={item.isActive}
                    />
                </View>
            )}
            columnWrapperStyle={isMdScreen ? { justifyContent: 'space-between' } : undefined}
            contentContainerStyle={{
                gap: 20,
                paddingBottom: floatingCardShow ? 90 : 20,
            }}
            contentContainerClassName="px-primary bg-background"
            showsVerticalScrollIndicator={false}
        />
    );
};

export default Offer;
