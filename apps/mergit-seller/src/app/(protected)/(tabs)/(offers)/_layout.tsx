import { TopTabs } from 'components/common';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';

import Offer from './index';
import TrackOffer from './track-offer';
import { View } from 'react-native';

const Tab = createMaterialTopTabNavigator();

const TABS = [
    { name: 'Offer', component: Offer },
    { name: 'Track Offer', component: TrackOffer },
];

export default function ProductTabsLayout() {
    return (
        <View className="flex-1 bg-background">
            <Tab.Navigator
                screenOptions={{ swipeEnabled: false }}
                tabBar={(props) => <TopTabs {...props} />}>
                {TABS.map((tab) => (
                    <Tab.Screen key={tab.name} name={tab.name} component={tab.component} />
                ))}
            </Tab.Navigator>
        </View>
    );
}
