import { View, Text, FlatList, useWindowDimensions, ScrollView } from 'react-native';
import { useState } from 'react';
import { fonts } from '@mergit-mobile/styles';
import { useBottomSheet, useCustomModal } from '@mergit-mobile/components';
import {
    ActiveOfferModel,
    OfferFilter,
    OfferSucces,
    OtpVerificationSheet,
    TrackOfferCard,
} from '~/components/offers';
import { useOrderStore } from '~/services/orders';

const offerData = [
    {
        id: 1,
        isActive: true,
        discountText: '30% OFF up to ₹75',
        startDate: '1 Mar 2022',
        grossSales: '₹7,890',
        ordersDelivered: 38,
        effectiveDiscount: '24%',
        minOrderValue: '₹159',
        offerApplicableFor: 'All users on all menu items, excluding MRP items',
        restaurantName: 'My Top Restaurant, City Center',
        restaurantId: '8928282',
    },
    {
        id: 2,
        isActive: false,
        discountText: '30% OFF up to ₹120',
        startDate: '1 Mar 2022',
        grossSales: '₹7,890',
        ordersDelivered: 38,
        effectiveDiscount: '24%',
        minOrderValue: '₹159',
        offerApplicableFor: 'All users on all menu items, excluding MRP items',
        restaurantName: 'My Top Restaurant, City Center',
        restaurantId: '8928282',
    },
];

const TrackOffer = () => {
    const [active, setActive] = useState('Active');
    const data = ['All Offers', 'Active', 'Up Coming', 'In-Active'];
    const { showModal } = useCustomModal();
    const { showBottomSheet } = useBottomSheet();
    const { floatingCardShow } = useOrderStore();

    const { width } = useWindowDimensions();
    const isMdScreen = width >= 768;

    const handleSuccessModel = (offer: any) => {
        showModal(<OfferSucces offer={offer} />);
    };

    const handleOtpBottomSheet = (offer: any) => {
        showBottomSheet(() => <OtpVerificationSheet onPress={() => handleSuccessModel(offer)} />);
    };

    const handleToggleStatus = (offer: any) => {
        showModal(<ActiveOfferModel offer={offer} onProceed={() => handleOtpBottomSheet(offer)} />);
    };

    const handleSelect = (item: string) => {
        setActive(item);
    };

    return (
        <ScrollView
            contentContainerClassName="flex-grow gap-5 px-primary bg-background"
            contentContainerStyle={{ paddingBottom: floatingCardShow ? 90 : 20 }}
            showsVerticalScrollIndicator={false}>
            <View className="flex-row items-center gap-4">
                <Text
                    style={fonts.fontSemiBold}
                    className="flex-1 text-text16 tracking-wide text-secondary">
                    Your offers
                </Text>
                <OfferFilter data={data} active={active} onSelect={handleSelect} />
            </View>

            <FlatList
                data={offerData}
                keyExtractor={(item) => item.id.toString()}
                numColumns={isMdScreen ? 2 : 1}
                renderItem={({ item }) => (
                    <View className="w-full md:w-[49%]">
                        <TrackOfferCard
                            offerData={item}
                            onToggleStatus={() => handleToggleStatus(item)}
                        />
                    </View>
                )}
                columnWrapperStyle={isMdScreen ? { justifyContent: 'space-between' } : undefined}
                contentContainerStyle={{ gap: 20 }}
                contentContainerClassName="bg-background"
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
            />
        </ScrollView>
    );
};

export default TrackOffer;
