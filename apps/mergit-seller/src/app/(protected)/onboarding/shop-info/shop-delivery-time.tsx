import { View, Text, ScrollView, Pressable } from 'react-native';
import { useState } from 'react';
import { fonts } from '@mergit-mobile/styles';
import { Header } from 'components/common';
import { CustomBtn } from '@mergit-mobile/components';
import { DateSelection, DayTimeSelection, TimePicker, TimeSlots } from 'components/onboarding';
import { formatTimeInput } from '~/services/onboarding';
import { router } from 'expo-router';
import { useClientStore } from '~/services/client';
import { useTabsStore } from '~/services/tabs';

const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

type Slot = { open: string; close: string };
type DaySlots = Record<string, Slot[]>;

const ShopDeliveryTime = () => {
    const [selectedDays, setSelectedDays] = useState<string[]>([]);
    const [sameTime, setSameTime] = useState(true);
    const [slots, setSlots] = useState([{ open: '', close: '' }]);
    const [showPicker, setShowPicker] = useState(false);
    const [activeSlot, setActiveSlot] = useState<{
        index: number;
        key: 'open' | 'close';
        day?: string;
    } | null>(null);
    const [dayActiveMap, setDayActiveMap] = useState<Record<string, boolean>>(() =>
        days.reduce((acc, day) => ({ ...acc, [day]: true }), {})
    );

    const { selectedService } = useClientStore();
    const { bottomInset } = useTabsStore();

    const toggleDay = (day: string) => {
        setSelectedDays((prev) =>
            prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
        );
    };

    const selectAllDays = () => {
        if (selectedDays.length === days.length) {
            setSelectedDays([]);
        } else {
            setSelectedDays(days);
        }
    };

    const addSlot = () => {
        setSlots([...slots, { open: '', close: '' }]);
    };

    const toggleDaySwitch = (day: string) => {
        setDayActiveMap((prev) => ({
            ...prev,
            [day]: !prev[day],
        }));
    };

    const removeSlot = (index: number) => {
        const updated = [...slots];
        updated.splice(index, 1);
        setSlots(updated);
    };

    const [dayWiseSlots, setDayWiseSlots] = useState<DaySlots>(() =>
        days.reduce((acc, day) => {
            acc[day] = [{ open: '', close: '' }];
            return acc;
        }, {} as DaySlots)
    );

    const handleDaySlotChange = (
        day: string,
        index: number,
        key: 'open' | 'close',
        value: string
    ) => {
        const updated = { ...dayWiseSlots };
        updated[day][index][key] = formatTimeInput(value);
        setDayWiseSlots(updated);
    };

    const addDaySlot = (day: string) => {
        const updated = { ...dayWiseSlots };
        updated[day].push({ open: '', close: '' });
        setDayWiseSlots(updated);
    };

    const removeDaySlot = (day: string, index: number) => {
        const updated = { ...dayWiseSlots };
        updated[day].splice(index, 1);
        setDayWiseSlots(updated);
    };

    const handleSubmit = () => {
        let submissionData: any;

        if (sameTime) {
            submissionData = {
                type: 'same',
                days: selectedDays,
                slots: slots.filter((slot) => slot.open && slot.close),
            };
        } else {
            const filteredDayWiseSlots = Object.keys(dayWiseSlots)
                .filter((day) => selectedDays.includes(day) && dayActiveMap[day])
                .reduce((acc, day) => {
                    acc[day] = dayWiseSlots[day].filter((slot) => slot.open && slot.close);
                    return acc;
                }, {} as DaySlots);

            submissionData = {
                type: 'day-wise',
                slots: filteredDayWiseSlots,
            };
        }

        console.log('Submitting:', submissionData);

        if (selectedService?.value !== 'food') {
            router.push('/onboarding/shop-info/select-categories');
        } else {
            router.push('/onboarding/successscreen?completedScreen=Shop Details');
        }
    };

    return (
        <View className="flex-1 bg-white">
            <Header
                backArrow
                titleLeft="Food Delivery Timings"
                stepwithIcon
                stepPregress={{ currentStep: 3, totalNumberOfSteps: 4 }}
                shadowBottom
                onPressHelpandsupport={() => router.push('/onboarding/faq')}
            />
            <ScrollView
                contentContainerStyle={{ flexGrow: 1, paddingBottom: bottomInset }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled">
                <View className="flex-1 gap-9 p-primary">
                    <Text
                        style={fonts.fontSemiBold}
                        className="text-text17 tracking-wide text-secondary">
                        Food Delivery Timings
                    </Text>

                    <DateSelection
                        selectedDays={selectedDays}
                        toggleDay={toggleDay}
                        selectAllDays={selectAllDays}
                    />

                    <View className="gap-4">
                        <Text
                            style={fonts.fontSemiBold}
                            className="text-text16 tracking-wide text-secondary">
                            Opening & Closing time
                        </Text>
                        <View className="gap-3">
                            <Pressable
                                className="flex-row items-start gap-3"
                                onPress={() => setSameTime(true)}>
                                <CustomRadio selected={sameTime} />
                                <Text
                                    className="flex-1 text-text15 tracking-wide text-secondary"
                                    style={fonts.fontMedium}>
                                    I open and close my shop at the same time on all working days
                                </Text>
                            </Pressable>
                            <Pressable
                                className="flex-row items-start gap-3"
                                onPress={() => setSameTime(false)}>
                                <CustomRadio selected={!sameTime} />
                                <Text
                                    className="flex-1 text-text15 tracking-wide text-secondary"
                                    style={fonts.fontMedium}>
                                    I've separate day wise timings
                                </Text>
                            </Pressable>
                        </View>
                    </View>

                    <View className="w-full flex-1 gap-10">
                        {sameTime ? (
                            <TimeSlots
                                slots={slots}
                                addSlot={addSlot}
                                removeSlot={removeSlot}
                                setActiveSlot={setActiveSlot}
                                setShowPicker={setShowPicker}
                            />
                        ) : (
                            days.map((day) => {
                                const isSelected = selectedDays.includes(day);
                                const isActive = isSelected && dayActiveMap[day];

                                return (
                                    <DayTimeSelection
                                        key={day}
                                        day={day}
                                        isSelected={isSelected}
                                        isActive={isActive}
                                        dayWiseSlots={dayWiseSlots[day]}
                                        handleDaySlotChange={handleDaySlotChange}
                                        addDaySlot={addDaySlot}
                                        removeDaySlot={removeDaySlot}
                                        setActiveSlot={setActiveSlot}
                                        setShowPicker={setShowPicker}
                                        toggleDaySwitch={toggleDaySwitch}
                                    />
                                );
                            })
                        )}
                    </View>

                    <View className="mt-10">
                        <CustomBtn testID="submit" title="Submit" onPress={handleSubmit} />
                    </View>

                    <TimePicker
                        visible={showPicker}
                        onClose={() => setShowPicker(false)}
                        onConfirm={(time) => {
                            const formatted = formatTimeInput(time);
                            if (activeSlot) {
                                if (sameTime) {
                                    const updated = [...slots];
                                    updated[activeSlot.index][activeSlot.key] = formatted;
                                    setSlots(updated);
                                } else if (activeSlot.day) {
                                    const updated = { ...dayWiseSlots };
                                    updated[activeSlot.day][activeSlot.index][activeSlot.key] =
                                        formatted;
                                    setDayWiseSlots(updated);
                                }
                                setShowPicker(false);
                                setActiveSlot(null);
                            }
                        }}
                        value={
                            activeSlot
                                ? activeSlot.day
                                    ? dayWiseSlots[activeSlot.day][activeSlot.index][activeSlot.key]
                                    : slots[activeSlot.index][activeSlot.key]
                                : undefined
                        }
                    />
                </View>
            </ScrollView>
        </View>
    );
};

export default ShopDeliveryTime;

const CustomRadio = ({ selected }: { selected: boolean }) => {
    return (
        <View
            className={`h-6 w-6 rounded-full border-2 ${
                selected ? 'border-primary' : 'border-gray-400'
            } items-center justify-center`}>
            {selected && <View className="h-3 w-3 rounded-full bg-primary" />}
        </View>
    );
};
