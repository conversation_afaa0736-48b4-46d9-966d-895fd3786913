import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useState } from 'react';
import { Header } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { CustomBtn, useBottomSheet } from '@mergit-mobile/components';
import { Feather, Ionicons } from '@expo/vector-icons';
import { colors } from '~/utils/constants';
import { router } from 'expo-router';
import { useTabsStore } from '~/services/tabs';

const CUISINE_OPTIONS = ['South Indian', 'North Indian', 'Chinese', 'Italian', 'Mexican'];

const SelectCategories = () => {
    const [tempSelected, setTempSelected] = useState<string[]>([]);
    const { bottomInset } = useTabsStore();
    const { showBottomSheet, hideBottomSheet } = useBottomSheet();

    const openBottomSheet = () => {
        showBottomSheet(() => (
            <View className="gap-5 px-primary py-primary" style={{ paddingBottom: bottomInset }}>
                <View className="flex-row items-center gap-5">
                    <Text
                        className="flex-1 text-text16 tracking-wide text-secondary"
                        style={fonts.fontSemiBold}>
                        Select Cuisines
                    </Text>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={hideBottomSheet}
                        className="h-9 w-9 items-center justify-center rounded-lg border border-primary">
                        <Ionicons name="close" size={24} color="#1F7575" />
                    </TouchableOpacity>
                </View>

                <View className="flex-row flex-wrap items-center gap-2">
                    {CUISINE_OPTIONS.map((cuisine) => {
                        const selected = tempSelected.includes(cuisine);
                        return (
                            <TouchableOpacity
                                key={cuisine}
                                activeOpacity={0.8}
                                onPress={() => toggleCuisine(cuisine)}
                                className={`flex-row items-center gap-1 rounded-lg border px-4 py-3 ${
                                    selected ? 'border-primary' : 'border-border'
                                }`}>
                                <Text
                                    style={fonts.fontMedium}
                                    className={`text-text15 tracking-wider ${selected ? 'text-primary' : 'text-secondary'}`}>
                                    {cuisine}
                                </Text>
                                {selected && (
                                    <TouchableOpacity
                                        activeOpacity={0.9}
                                        onPress={() => toggleCuisine(cuisine)}>
                                        <Ionicons name="close" size={20} color={colors.primary} />
                                    </TouchableOpacity>
                                )}
                            </TouchableOpacity>
                        );
                    })}
                </View>
            </View>
        ));
    };

    const toggleCuisine = (cuisine: string) => {
        if (tempSelected.includes(cuisine)) {
            setTempSelected((prev) => prev.filter((c) => c !== cuisine));
        } else {
            setTempSelected((prev) => [...prev, cuisine]);
        }
    };

    const handleBottomSheetSubmit = () => {
        hideBottomSheet();
        router.push('/onboarding/successscreen?completedScreen=Shop Details');
    };

    const removeCuisineFromMain = (cuisine: string) => {
        const updated = tempSelected.filter((c) => c !== cuisine);
        setTempSelected(updated);
    };

    return (
        <View className="flex-1 bg-white">
            <Header
                backArrow
                titleLeft="Select Cuisines"
                stepwithIcon
                stepPregress={{ currentStep: 4, totalNumberOfSteps: 4 }}
                onPressHelpandsupport={() => router.push('/onboarding/faq')}
            />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerClassName="flex-grow gap-5 p-primary"
                contentContainerStyle={{ paddingBottom: bottomInset }}>
                <Text
                    style={fonts.fontSemiBold}
                    className="text-text17 tracking-wide text-secondary">
                    Select Cuisines
                </Text>

                <Text style={fonts.fontMedium} className="text-text17 text-secondary">
                    Food Cuisines You Serve*
                </Text>

                <View className="flex-1 gap-5">
                    <View className="flex-row flex-wrap items-center gap-2">
                        {tempSelected.map((cuisine) => (
                            <View
                                key={cuisine}
                                className="flex-row items-center gap-2 rounded-lg border border-border bg-white px-4 py-3">
                                <Text
                                    style={fonts.fontMedium}
                                    className="text-text15 tracking-wide text-secondary">
                                    {cuisine}
                                </Text>
                                <TouchableOpacity
                                    activeOpacity={0.8}
                                    onPress={() => removeCuisineFromMain(cuisine)}>
                                    <Ionicons name="close" size={18} color={colors.neutral} />
                                </TouchableOpacity>
                            </View>
                        ))}
                    </View>

                    <CustomBtn
                        title="Select Cuisines"
                        onPress={openBottomSheet}
                        borderColor={colors.primary}
                        backgroundColor="#fff"
                        textStyle={{ color: colors.primary }}
                        btnStyle={{ alignSelf: 'flex-start' }}
                        icon={<Feather name="plus" size={20} color={colors.primary} />}
                        position="left"
                        isShadow
                    />
                </View>

                <CustomBtn
                    onPress={handleBottomSheetSubmit}
                    title="Submit"
                    disabled={!tempSelected.length}
                />
            </ScrollView>
        </View>
    );
};

export default SelectCategories;
