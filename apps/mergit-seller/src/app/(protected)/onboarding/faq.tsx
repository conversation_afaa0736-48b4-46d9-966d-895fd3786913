import { View, Text, ScrollView } from 'react-native';
import { colors } from '~/utils/constants';
import { fonts } from '@mergit-mobile/styles';
import { CustomBtn, useBottomSheet, useCustomModal } from '@mergit-mobile/components';
import { SuccessFailure } from '@mergit-mobile/components';
import { Accordion } from '~/components/more/help-center';
import { Header } from '~/components/common';
import { useTabsStore } from '~/services/tabs';

const faqData = [
    {
        question: 'How do I register as a seller?',
        answer: `Yes, you can try us for free for 30 days. If you want, we'll provide you with a free, personalized 30–minute onboarding call to get you up and running as soon as possible.`,
    },
    {
        question: 'What are the requirements to start selling?',
        answer: 'You need to have a valid business license and a valid bank account.',
    },
    {
        question: 'How do I add products to my store?',
        answer: 'You can add products to your store by clicking on the "Add Product" button and filling out the product information.',
    },
    { question: 'What are the guidelines for product images and descriptions?' },
    { question: 'How do I manage my orders?' },
    { question: 'Can I cancel an order after confirmation?' },
    { question: 'Who handles shipping?' },
];

export default function FAQ() {
    const { showModal, hideModal } = useCustomModal();
    const { showBottomSheet, hideBottomSheet } = useBottomSheet();
    const { bottomInset } = useTabsStore();

    const confirmModel = () => {
        showModal(
            <View className="gap-3">
                <Text style={fonts.fontBold} className="text-text18 tracking-wide text-secondary">
                    Help
                </Text>
                <Text
                    style={fonts.fontRegular}
                    className="text-text15 tracking-wide text-secondary">
                    Are you sure you want our help?
                </Text>
                <View className="flex-row items-center gap-4">
                    <CustomBtn
                        title="Cancel"
                        onPress={hideModal}
                        backgroundColor="transparent"
                        borderColor={colors.primary}
                        textStyle={{ color: colors.primary }}
                        btnStyle={{ flex: 1 }}
                        height={45}
                    />
                    <CustomBtn
                        title="Confirm"
                        onPress={handleSuccessSheet}
                        btnStyle={{ flex: 1 }}
                        height={45}
                    />
                </View>
            </View>
        );
    };

    const handleSuccessSheet = () => {
        hideModal();
        showBottomSheet(() => (
            <View
                className="items-center justify-center gap-5 p-primary"
                style={{ paddingBottom: bottomInset }}>
                <SuccessFailure
                    status="success"
                    innerHeight={50}
                    innerWidth={50}
                    outerHeight={80}
                    outerWidth={80}
                    iconSize={30}
                />
                <Text style={fonts.fontSemiBold} className="text-text17 text-secondary">
                    Thank You
                </Text>
                <Text style={fonts.fontMedium} className="text-center text-text15 text-secondary">
                    Our team will get back to you shortly.
                </Text>
                <CustomBtn
                    title="Ok"
                    onPress={hideBottomSheet}
                    height={50}
                    btnStyle={{ width: '100%' }}
                />
            </View>
        ));
    };
    return (
        <View className="flex-1 bg-background">
            <Header backArrow titleLeft="FAQ" backgroundColor={colors.background} />

            <ScrollView
                contentContainerClassName="flex-grow px-primary gap-5"
                contentContainerStyle={{ paddingBottom: bottomInset }}
                showsVerticalScrollIndicator={false}>
                <View className="overflow-hidden rounded-lg border border-border bg-white">
                    {faqData.map((item, index) => (
                        <Accordion
                            key={index}
                            title={item.question}
                            content={item.answer}
                            isLast={index === faqData.length - 1}
                        />
                    ))}
                </View>

                <View className="gap-3 rounded-lg border border-border bg-white p-4">
                    <Text
                        style={fonts.fontSemiBold}
                        className="text-text15 tracking-wide text-primary">
                        Support
                    </Text>
                    <Text
                        style={fonts.fontSemiBold}
                        className="text-text16 tracking-wide text-secondary">
                        Didn't find what you're looking for? Reach Out to Us!
                    </Text>
                    <Text
                        style={fonts.fontMedium}
                        className="text-text15 tracking-wide text-neutral">
                        If your issue isn't covered in the FAQs, feel free to submit a query.Our
                        support team will review it and get back to you shortly.
                    </Text>

                    <CustomBtn
                        title="CallBack"
                        onPress={confirmModel}
                        height={45}
                        btnStyle={{
                            alignSelf: 'flex-start',
                        }}
                    />
                </View>
            </ScrollView>
        </View>
    );
}
