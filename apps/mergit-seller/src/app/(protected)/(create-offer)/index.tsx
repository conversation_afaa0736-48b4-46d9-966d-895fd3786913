import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { useState } from 'react';
import { Header } from 'components/common';
import { Feather } from '@expo/vector-icons';
import { fonts } from '@mergit-mobile/styles';
import { router } from 'expo-router';
import {
    DiscountApplicable,
    DiscountSelection,
    OfferType,
    OffercardProps,
    CampaignStart,
} from 'components/offers';
import { OfferApplicable } from 'components/offers/OfferApplicable';
import { CustomBtn, useBottomSheet } from '@mergit-mobile/components';
import { useCustomModal } from '@mergit-mobile/components';
import { ActiveOfferModel } from 'components/offers/ActiveOfferModel';
import { OtpVerificationSheet } from 'components/offers/OtpVerificationSheet';
import { OfferSucces } from 'components/offers/OfferSucces';
import { useTabsStore } from '~/services/tabs';
import { useOrderStore } from '~/services/orders';

const CreateOffer = () => {
    const [selectedOfferType, setSelectedOfferType] = useState<string | null>('');
    const { showBottomSheet, hideBottomSheet } = useBottomSheet();
    const { showModal } = useCustomModal();
    const { bottomInset } = useTabsStore();
    const { floatingCardShow } = useOrderStore();

    const handleToggleStatus = (offer: any) => {
        showModal(<ActiveOfferModel offer={offer} onProceed={() => handleOtpBottomSheet(offer)} />);
    };

    const offerApplicable = (offer: OffercardProps | any) => {
        showBottomSheet(() => (
            <OfferApplicable
                offer={offer}
                onClose={hideBottomSheet}
                onApplayOffer={() => console.log('Offer Applicable')}
            />
        ));
    };

    const handleSuccessModel = (offer: any) => {
        showModal(<OfferSucces offer={offer} />);
    };

    const handleOtpBottomSheet = (offer: any) => {
        showBottomSheet(() => <OtpVerificationSheet onPress={() => handleSuccessModel(offer)} />);
    };

    return (
        <View className="flex-1 bg-background">
            <Header shadowBottom shopHeader />
            <ScrollView
                contentContainerStyle={{
                    flexGrow: 1,
                    paddingBottom: floatingCardShow ? 90 + bottomInset : bottomInset,
                }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled">
                <View className="flex-1 gap-5 px-primary pt-primary">
                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={router.back}
                        className="flex-row items-center gap-3">
                        <Feather name="arrow-left" size={24} color="black" />
                        <Text style={fonts.fontSemiBold} className="text-text18 text-secondary">
                            Create Offer
                        </Text>
                    </TouchableOpacity>
                    <OfferType
                        onSelect={(selectedOfferType) => setSelectedOfferType(selectedOfferType)}
                    />

                    <DiscountSelection offerType={selectedOfferType || 'Percentage Discount'} />

                    <DiscountApplicable onOfferApplicable={() => offerApplicable('')} />

                    <CampaignStart offerTyepe={selectedOfferType || 'Percentage Discount'} />

                    <CustomBtn
                        title="Create Offer"
                        onPress={() => handleToggleStatus({ isActive: true })}
                        height={50}
                    />
                </View>
            </ScrollView>
        </View>
    );
};

export default CreateOffer;
