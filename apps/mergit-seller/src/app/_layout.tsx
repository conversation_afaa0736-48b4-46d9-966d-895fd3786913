import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { BottomSheetProvider, CustomModalProvider } from '@mergit-mobile/components';
import { Stack } from 'expo-router';
import { useClientStore } from '~/services/client';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient, startTokenRefreshScheduler, stopTokenRefreshScheduler } from '~/helpers';
import { CustomToastProvider, GlobalProvider } from '~/context';
import { Keyboard, Platform } from 'react-native';
import { useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTabsStore } from '~/services/tabs';
import { OneSignal } from 'react-native-onesignal';

const oneSignalId = process.env.EXPO_PUBLIC_NOTIFICATION_APPID! || '';
OneSignal.initialize(oneSignalId);

export default function RootLayout() {
    const { token } = useClientStore();
    const { setBottomInset } = useTabsStore();
    const { bottom } = useSafeAreaInsets();

    useEffect(() => {
        if (token) {
            startTokenRefreshScheduler();
        } else {
            stopTokenRefreshScheduler();
        }
        return () => {
            stopTokenRefreshScheduler();
        };
    }, [token]);

    useEffect(() => {
        const subscribeHide = Keyboard.addListener('keyboardDidHide', () => {
            if (Platform.OS === 'android') {
                setBottomInset(bottom + 8);
            } else {
                setBottomInset(25);
            }
        });
        const subscribeShow = Keyboard.addListener('keyboardDidShow', () => {
            setBottomInset(20);
        });

        return () => {
            subscribeHide.remove();
            subscribeShow.remove();
        };
    }, []);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <GlobalProvider>
                <CustomToastProvider>
                    <QueryClientProvider client={queryClient}>
                        <KeyboardProvider>
                            <CustomModalProvider>
                                <BottomSheetProvider>
                                    <Stack
                                        screenOptions={{
                                            headerShown: false,
                                            animation: 'slide_from_right',
                                        }}>
                                        <Stack.Protected guard={!token}>
                                            <Stack.Screen name="(auth)" />
                                        </Stack.Protected>
                                        <Stack.Protected guard={!!token}>
                                            <Stack.Screen name="(protected)" />
                                        </Stack.Protected>
                                        <Stack.Screen name="policies" />
                                    </Stack>
                                </BottomSheetProvider>
                            </CustomModalProvider>
                        </KeyboardProvider>
                    </QueryClientProvider>
                </CustomToastProvider>
            </GlobalProvider>
        </GestureHandlerRootView>
    );
}
