import { ExpoConfig, ConfigContext } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => {
    const googleMapApi = process.env.EXPO_PUBLIC_GOOGLE_MAP_API_KEY;
    const notifyEnv = process.env.EXPO_PUBLIC_ONESIGNAL_ENV || 'development';

    return {
        ...config,
        name: 'Mergit Seller',
        slug: 'mergit-seller',
        version: '1.0.0',
        orientation: 'portrait',
        newArchEnabled: true,
        scheme: 'mergit-seller',
        userInterfaceStyle: 'light',
        icon: './src/assets/images/icon.png',
        splash: {
            image: './src/assets/images/splash.png',
            resizeMode: 'contain',
            backgroundColor: '#1F7575',
        },
        updates: {
            fallbackToCacheTimeout: 0,
        },
        assetBundlePatterns: ['**/*'],
        ios: {
            supportsTablet: true,
            bundleIdentifier: 'com.mergit.seller',
            config: {
                googleMapsApiKey: googleMapApi,
            },
            infoPlist: {
                NSLocationWhenInUseUsageDescription:
                    'We need your location to provide location-based services.',
                NSCameraUsageDescription: 'Allow $(PRODUCT_NAME) to access your camera.',
                NSPhotoLibraryUsageDescription: 'Allow $(PRODUCT_NAME) to access your photos.',
                UIBackgroundModes: ['remote-notification'],
            },
            entitlements: {
                'aps-environment': notifyEnv,
                'com.apple.security.application-groups': ['group.com.mergit.seller.onesignal'],
            },
        },
        android: {
            adaptiveIcon: {
                foregroundImage: './src/assets/images/adaptiveicon.png',
                backgroundColor: '#FFFFFF',
            },
            package: 'com.mergit.seller',
            config: {
                googleMaps: {
                    apiKey: googleMapApi,
                },
            },
            permissions: ['ACCESS_FINE_LOCATION'],
            edgeToEdgeEnabled: true,
        },
        web: {
            favicon: './src/assets/images/favicon.png',
            bundler: 'metro',
            output: 'static',
        },
        plugins: [
            'expo-router',
            'expo-web-browser',
            './src/plugins/app-theme',
            [
                'expo-build-properties',
                {
                    android: {
                        minSdkVersion: 26,
                        targetSdkVersion: 35,
                        compileSdkVersion: 35,
                        buildToolsVersion: '35.0.0',
                    },
                    ios: {
                        deploymentTarget: '15.5',
                    },
                },
            ],
            [
                'onesignal-expo-plugin',
                {
                    mode: notifyEnv,
                },
            ],
            [
                'expo-font',
                {
                    fonts: ['./src/assets/fonts'],
                },
            ],
            [
                'expo-location',
                {
                    locationAlwaysAndWhenInUsePermission:
                        'Allow $(PRODUCT_NAME) to use your location.',
                },
            ],
            [
                'expo-image-picker',
                {
                    photosPermission:
                        'The app accesses your photos to let you share them with your friends.',
                },
            ],
        ],
        extra: {
            router: {
                origin: false,
            },
            eas: {
                projectId: '35acc23b-e6d7-40b3-a4b2-7a9fdd51cc9c',
            },
        },
        experiments: {
            typedRoutes: true,
            tsconfigPaths: true,
        },
    };
};
