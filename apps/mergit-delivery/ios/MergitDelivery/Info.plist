<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>Mergit Delivery</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleURLTypes</key>
    <array>
      <dict>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>mergit-delivery</string>
          <string>com.mergit.delivery</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>exp+mergit-delivery</string>
        </array>
      </dict>
    </array>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>GMSApiKey</key>
    <string>AIzaSyB3XeNWp6x5nk4F4o9Azm_3pNXudxnI18M</string>
    <key>LSMinimumSystemVersion</key>
    <string>12.0</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>NSAppTransportSecurity</key>
    <dict>
      <key>NSAllowsArbitraryLoads</key>
      <false/>
      <key>NSAllowsLocalNetworking</key>
      <true/>
    </dict>
    <key>NSCameraUsageDescription</key>
    <string>Allow $(PRODUCT_NAME) to access your camera</string>
    <key>NSContactsUsageDescription</key>
    <string>Allow $(PRODUCT_NAME) to access your contacts to show and invaite your friends.</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>Allow $(PRODUCT_NAME) to use your location.</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>We need access to your location to enhance your experience.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>We need your location to provide location-based services.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>Allow $(PRODUCT_NAME) to access your microphone</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>The app accesses your photos to let you share them with your friends.</string>
    <key>NSUserActivityTypes</key>
    <array>
      <string>$(PRODUCT_BUNDLE_IDENTIFIER).expo.index_route</string>
    </array>
    <key>UIAppFonts</key>
    <array>
      <string>DMSans-Bold.ttf</string>
      <string>DMSans-BoldItalic.ttf</string>
      <string>DMSans-ExtraBold.ttf</string>
      <string>DMSans-ExtraBoldItalic.ttf</string>
      <string>DMSans-ExtraLight.ttf</string>
      <string>DMSans-ExtraLightItalic.ttf</string>
      <string>DMSans-Italic.ttf</string>
      <string>DMSans-Light.ttf</string>
      <string>DMSans-LightItalic.ttf</string>
      <string>DMSans-Medium.ttf</string>
      <string>DMSans-MediumItalic.ttf</string>
      <string>DMSans-Regular.ttf</string>
      <string>DMSans-SemiBold.ttf</string>
      <string>DMSans-SemiBoldItalic.ttf</string>
      <string>DMSans-Thin.ttf</string>
      <string>DMSans-ThinItalic.ttf</string>
    </array>
    <key>UIBackgroundModes</key>
    <array>
      <string>remote-notification</string>
      <string>location</string>
    </array>
    <key>UILaunchStoryboardName</key>
    <string>SplashScreen</string>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
      <string>arm64</string>
    </array>
    <key>UIRequiresFullScreen</key>
    <false/>
    <key>UIStatusBarStyle</key>
    <string>UIStatusBarStyleDefault</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UIUserInterfaceStyle</key>
    <string>Light</string>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
  </dict>
</plist>