<resources xmlns:tools="http://schemas.android.com/tools">
  <style name="AppTheme" parent="Theme.EdgeToEdge">
    <item name="android:navigationBarColor">@android:color/white</item>
    <item name="android:enforceNavigationBarContrast" tools:targetApi="q">false</item>
    <item name="android:windowTranslucentNavigation">false</item>
    <item name="android:windowIsTranslucent">false</item>
    <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="p">shortEdges</item>
    <item name="android:windowLightStatusBar">true</item>
  </style>
  <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>