import { OrderData } from 'services/orders';

export const RiderDetailsData = [
    {
        id: 1,
        name: '<PERSON>',
        zone: 'Vadapalani',
        city: 'Chennai',
        vehicleNumber: 'TN01AB1234',
    },
];

export const ProfileData = {
    name: '<PERSON>',
    phone: '+91 1234567890',
    email: '<EMAIL>',
    image: 'https://i.ibb.co/v48F6sGD/male-user.png',
};

export const PersonalDetailsData = {
    phone: '+91 1234567890',
    aadharNumber: '1234 5678 9012',
    panNumber: '**********',
};

export type LiveOrder = {
    id: number;
    orderId: string;
    shopName: string;
    earnings: number;
    status: string;
    address: string;
    duration: string;
};

export const liveOrders: LiveOrder[] = [
    {
        id: 1,
        orderId: '123456',
        shopName: 'Sathya Electronics',
        earnings: 500,
        status: 'Ready',
        address: 'No.1a, Methu Street, Ekkatuthangal, Guindy, Chennai',
        duration: '5 mins',
    },
    {
        id: 2,
        orderId: '234567',
        shopName: 'SS Hyderabad Briyani',
        earnings: 300,
        status: 'Ready',
        address: 'No.1a, Methu Street, Ekkatuthangal, Guindy, Chennai',
        duration: '6 mins',
    },
];

export type ShiftTimingsType = {
    id: string;
    name: string;
    data: {
        id: string;
        title: string;
        isBooked: boolean;
    }[];
};

export const shiftTimings: ShiftTimingsType[] = [
    {
        id: 'morning-shift',
        name: 'Morning',
        data: [
            {
                id: 'morning-shift-1',
                title: '6:00 AM - 7.00 AM',
                isBooked: true,
            },
            {
                id: 'morning-shift-2',
                title: '7:00 AM - 9.00 AM',
                isBooked: true,
            },
            {
                id: 'morning-shift-3',
                title: '10:00 AM - 11.00 AM',
                isBooked: false,
            },
            {
                id: 'morning-shift-4',
                title: '11:00 AM - 12.00 PM',
                isBooked: false,
            },
        ],
    },
    {
        id: 'afternoon-shift',
        name: 'Afternoon',
        data: [
            {
                id: 'afternoon-shift-1',
                title: '12:00 PM - 1.00 PM',
                isBooked: false,
            },
            {
                id: 'afternoon-shift-2',
                title: '1:00 PM - 2.00 PM',
                isBooked: false,
            },
            {
                id: 'afternoon-shift-3',
                title: '2:00 PM - 3.00 PM',
                isBooked: false,
            },
            {
                id: 'afternoon-shift-4',
                title: '3:00 PM - 4.00 PM',
                isBooked: false,
            },
            {
                id: 'afternoon-shift-5',
                title: '4:00 PM - 5.00 PM',
                isBooked: false,
            },
            {
                id: 'afternoon-shift-6',
                title: '5:00 PM - 6.00 PM',
                isBooked: false,
            },
        ],
    },
    {
        id: 'night-shift',
        name: 'Night',
        data: [
            {
                id: 'night-shift-1',
                title: '6:00 PM - 7.00 PM',
                isBooked: false,
            },
            {
                id: 'night-shift-2',
                title: '8:00 PM - 9.00 PM',
                isBooked: false,
            },
            {
                id: 'night-shift-3',
                title: '9:00 PM - 10.00 PM',
                isBooked: false,
            },
            {
                id: 'night-shift-4',
                title: '10:00 PM - 11.00 PM',
                isBooked: false,
            },
            {
                id: 'night-shift-5',
                title: '11:00 PM - 12.00 PM',
                isBooked: false,
            },
        ],
    },
];

export const videoList = {
    liveOrder: [
        {
            id: 'live1',
            title: 'Introduction to Mergit',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/cycling+1.mp4',
            thumbnail: 'https://i.ibb.co/NDh40gs/image-8.png',
        },
        {
            id: 'live2',
            title: 'How Orders Work',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/girlwalking+1.mp4',
            thumbnail: 'https://i.ibb.co/MDJL6gGV/image.png',
        },
        {
            id: 'live3',
            title: 'Delivery Flow',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/sunflower+1.mp4',
            thumbnail: 'https://i.ibb.co/ymkxhwQw/image-1.png',
        },
    ],
    supportTicket: [
        {
            id: 'support1',
            title: 'Introduction to Support',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/girlwalking+1.mp4',
            thumbnail: 'https://i.ibb.co/MDJL6gGV/image.png',
        },
        {
            id: 'support2',
            title: 'Ticket Management',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/girlwalking+1.mp4',
            thumbnail: 'https://i.ibb.co/ymkxhwQw/image-1.png',
        },
        {
            id: 'support3',
            title: 'Escalation Process',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/sunflower+1.mp4',
            thumbnail: 'https://i.ibb.co/NDh40gs/image-8.png',
        },
    ],
    earnMore: [
        {
            id: 'earn1',
            title: 'Referral Program',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/sunflower+1.mp4',
            thumbnail: 'https://i.ibb.co/ymkxhwQw/image-1.png',
        },
        {
            id: 'earn2',
            title: 'Tips & Tricks',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/girlwalking+1.mp4',
            thumbnail: 'https://i.ibb.co/NDh40gs/image-8.png',
        },
        {
            id: 'earn3',
            title: 'Extra Earnings',
            videourl:
                'https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/sunflower+1.mp4',
            thumbnail: 'https://i.ibb.co/MDJL6gGV/image.png',
        },
    ],
};

export const newData: OrderData = {
    partnerId: 'partner_6qd7jMBhUm',
    weight: 1000,
    orderCount: 1,
    overAllETA: {
        distance: {
            valueMeters: 2839,
            valueKilometers: 2.839,
            text: '2.84 km',
        },
        duration: {
            valueSeconds: 678,
            valueMinutes: 11,
            valueRemainingSeconds: 18,
            text: '11 min 18 sec',
        },
    },
    routes: [
        {
            location: {
                lat: 13.0216921,
                lng: 80.2073368,
            },
            userType: 'partner',
            id: 'partner_6qd7jMBhUm',
            orders: [
                {
                    orderId: 'ORD_9MX2wFkLkK',
                    subOrderId: 'SUB_6TVkjgrFth',
                    timestamps: {
                        assignedAt: null,
                        acceptedAt: '2025-05-28T11:51:55.211Z',
                        pickedUpAt: null,
                        reachedDropAt: null,
                        rejectedAt: null,
                        deliveredAt: null,
                        reachedPickUpAt: null,
                    },
                },
                {
                    orderId: 'ORD_9MX2wFkLkK',
                    subOrderId: 'SUB_i3FXsBRgGe',
                    timestamps: {
                        assignedAt: null,
                        acceptedAt: '2025-05-28T11:51:55.211Z',
                        pickedUpAt: null,
                        reachedDropAt: null,
                        rejectedAt: null,
                        deliveredAt: null,
                        reachedPickUpAt: null,
                    },
                },
            ],
        },
        {
            id: 'seller_qHVlXFod65',
            userType: 'seller',
            location: {
                lat: 13.02421,
                lng: 80.206916,
            },
            name: 'Layalee Global Cuisine Restaurant',
            contact: '9867452389',
            address: null,
            orders: [
                {
                    subOrderId: 'SUB_6TVkjgrFth',
                    orderId: 'ORD_9MX2wFkLkK',
                    preparingTime: {
                        text: '1 min 40 sec',
                        value: '100',
                    },
                    items: [
                        {
                            categoryId: 'category_3KiQRpWE7C',
                            subCategoryId: 'subcategory_99eeHatU19',
                            productName: 'Meals',
                            quantity: 2,
                            dishPrice: 250,
                            gst: 18,
                            packagePrice: 10,
                            finalDishPrice: 295,
                            weight: 250,
                        },
                    ],
                    weight: 500,
                    status: 'delivered',
                    userId: 'user_4cAYibejMB',
                },
                {
                    subOrderId: 'SUB_i3FXsBRgGe',
                    orderId: 'ORD_9MX2wFkLkK',
                    preparingTime: {
                        text: '1 min 40 sec',
                        value: '100',
                    },
                    items: [
                        {
                            categoryId: 'category_5809bS1b99',
                            subCategoryId: 'subcategory_99eEHatU09',
                            productName: 'Meals',
                            quantity: 2,
                            dishPrice: 250,
                            gst: 18,
                            packagePrice: 10,
                            finalDishPrice: 295,
                            weight: 250,
                        },
                    ],
                    weight: 500,
                    status: 'preparing',
                    userId: 'user_4cAYibejMB',
                },
            ],
        },
        {
            id: 'user_4cAYibejMB',
            userType: 'user',
            location: {
                lat: 13.021325,
                lng: 80.200982,
            },
            name: 'Maari',
            contact: '',
            address: null,
            orders: [
                {
                    subOrderId: 'SUB_6TVkjgrFth',
                    orderId: 'ORD_9MX2wFkLkK',
                    sellerId: 'seller_qHVlXFod65',
                    shopName: 'Layalee Global Cuisine Restaurant',
                    items: [
                        {
                            categoryId: 'category_3KiQRpWE7C',
                            subCategoryId: 'subcategory_99eeHatU19',
                            productName: 'Meals',
                            quantity: 2,
                            dishPrice: 250,
                            gst: 18,
                            packagePrice: 10,
                            finalDishPrice: 295,
                            weight: 250,
                        },
                    ],
                    subTotal: 590,
                    status: 'delivered',
                    totalEarnings: 0,
                    totalDistance: {
                        km: 0.7184568481280903,
                        meters: 718,
                        text: '0.72 km',
                    },
                },
                {
                    subOrderId: 'SUB_i3FXsBRgGe',
                    orderId: 'ORD_9MX2wFkLkK',
                    sellerId: 'seller_XuyS793jAk',
                    shopName: 'Daalchini',
                    items: [
                        {
                            categoryId: 'category_5809bS1b99',
                            subCategoryId: 'subcategory_99eEHatU09',
                            productName: 'Meals',
                            quantity: 2,
                            dishPrice: 250,
                            gst: 18,
                            packagePrice: 10,
                            finalDishPrice: 295,
                            weight: 250,
                        },
                    ],
                    subTotal: 590,
                    status: 'preparing',
                    totalEarnings: 0,
                    totalDistance: {
                        km: 0.6159739534916593,
                        meters: 616,
                        text: ' 0.62 km ',
                    },
                },
            ],
        },
    ],
};


export const faqList = [
    {
        id: 'faq-1',
        title: 'pickupIssues',
        description: 'pickupIssuesDesc',
    },
    {
        id: 'faq-2',
        title: 'deliveryIssues',
        description: 'deliveryIssuesDesc',
    },
    {
        id: 'faq-3',
        title: 'paymentEarningsIssues',
        description: 'paymentEarningsIssuesDesc',
    },
    {
        id: 'faq-4',
        title: 'navigationAppIssues',
        description: 'navigationAppIssuesDesc',
    },
    {
        id: 'faq-5',
        title: 'vehicleSecurityIssues',
        description: 'vehicleSecurityIssuesDesc',
    },
];