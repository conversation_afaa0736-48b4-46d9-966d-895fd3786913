import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { mmkvStorage } from 'utils/storage';

export type LanguageType = 'en' | 'ta' | 'hi';

export interface AuthState {
    languageSelected: LanguageType | null;
    setLanguageSelected: (languageSelected: LanguageType | null) => void;

    token: string | null;
    login: (token: string) => void;
    logout: () => void;

    authToken: string | null;
    setAuthToken: (authToken: string) => void;
    removeAuthToken: () => void;

    onboardingCompleted: boolean;
    setOnBoradingComplete: (val: boolean) => void;

    refreshToken: string | null;
    setRefreshToken: (refreshToken: string) => void;
    removeRefreshToken: () => void;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set) => ({
            languageSelected: null,
            setLanguageSelected: (languageSelected) => set({ languageSelected }),

            token: null,
            login: (token) => set({ token }),
            logout: () => set({ token: null }),

            authToken: null,
            setAuthToken: (authToken) => set({ authToken }),
            removeAuthToken: () => set({ authToken: null }),

            onboardingCompleted: false,
            setOnBoradingComplete: (val) => set({ onboardingCompleted: val }),

            refreshToken: null,
            setRefreshToken: (refreshToken) => set({ refreshToken }),
            removeRefreshToken: () => set({ refreshToken: null }),
        }),
        {
            name: 'auth-store',
            storage: createJSONStorage(() => mmkvStorage),
            partialize: (state) => ({
                token: state.token,
                authToken: state.authToken,
                languageSelected: state.languageSelected,
                onboardingCompleted: state.onboardingCompleted,
                refreshToken: state.refreshToken,
            }),
        }
    )
);
