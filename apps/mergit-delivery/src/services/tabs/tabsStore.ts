import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { mmkvStorage } from '~/utils/storage';

type TabsStoreState = {
    tabbarHeight: number;
    setTabbarHeight: (height: number) => void;

    bottomInset: number;
    setBottomInset: (inset: number) => void;
};

export const useTabsStore = create<TabsStoreState>()(
    persist(
        (set) => ({
            tabbarHeight: 0,
            setTabbarHeight: (height) => set({ tabbarHeight: height }),

            bottomInset: 0,
            setBottomInset: (inset) => set({ bottomInset: inset }),
        }),
        {
            name: 'mergit-seller',
            storage: createJSONStorage(() => mmkvStorage),
            partialize: (state) => ({
                tabbarHeight: state.tabbarHeight,
                bottomInset: state.bottomInset,
            }),
        }
    )
);
