import { NewSeller, OrderData, OrderPackageImages, Routes } from './orderTypes';
import { create } from 'zustand';

export interface OrderState {
    previousOrders: boolean;
    updatePreviousOrders: (val: boolean) => void;

    orders: OrderData | null;
    updateOrders: (order: OrderData | null) => void;

    orderPackageImages: OrderPackageImages[] | null;
    updateOrderPackageImages: (data: OrderPackageImages | null) => void;

    partnerOrders: Routes | null;
    updatePartnerOrders: (order: Routes | null) => void;

    userOrdersIndex: number;
    updateUserOrdersIndex: (val: number) => void;

    currentRouteStop: number;
    updateCurrentRouteStop: (val: number) => void;

    newSellers: NewSeller[] | null;
    updateNewSellers: (val: NewSeller[] | null) => void;

    orderAssigned: any | null;
    updateOrderAssigned: (data: any | null) => void;

    newOrderAssigned: any | null;
    updateNewOrderAssigned: (data: any | null) => void;

    sellerOrdersIndex: number;
    updateSellerOrdersIndex: (val: number) => void;
}

export const useOrderStore = create<OrderState>((set) => ({
    previousOrders: false,
    updatePreviousOrders: (val) => set({ previousOrders: val }),

    orders: null,
    updateOrders: (newOrder) => set({ orders: newOrder }),

    partnerOrders: null,
    updatePartnerOrders: (order) => set({ partnerOrders: order }),

    userOrdersIndex: -1,
    updateUserOrdersIndex: (val) => set({ userOrdersIndex: val }),

    currentRouteStop: -1,
    updateCurrentRouteStop: (val) => set({ currentRouteStop: val }),

    newSellers: [],
    updateNewSellers: (val) => set({ newSellers: val }),

    orderAssigned: null,
    updateOrderAssigned: (data) => set({ orderAssigned: data }),

    newOrderAssigned: null,
    updateNewOrderAssigned: (data) => set({ newOrderAssigned: data }),

    sellerOrdersIndex: -1,
    updateSellerOrdersIndex: (val) => set({ sellerOrdersIndex: val }),

    orderPackageImages: null,
    updateOrderPackageImages: (data) =>
        set((state) => {
            if (!data) return { orderPackageImages: null };

            const current = state.orderPackageImages ?? [];
            const index = current.findIndex((item) => item.subOrderId === data.subOrderId);

            let updated: OrderPackageImages[];

            if (index !== -1) {
                updated = [...current];
                updated[index] = data;
            } else {
                updated = [...current, data];
            }

            return { orderPackageImages: updated };
        }),
}));
