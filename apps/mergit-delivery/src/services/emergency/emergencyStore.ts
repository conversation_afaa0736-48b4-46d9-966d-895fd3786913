import { create } from 'zustand';

interface EmergencyContact {
    id: string;
    fullName: string;
    phoneNumber: string;
    gender: string;
}

interface EmergencyStore {
    contacts: EmergencyContact[];
    addContact: (contact: Omit<EmergencyContact, 'id'>) => void;
    updateContact: (id: string, contact: Omit<EmergencyContact, 'id'>) => void;
    removeContact: (id: string) => void;
}

export const useEmergencyStore = create<EmergencyStore>((set) => ({
    contacts: [
        {
            id: '1',
            fullName: '<PERSON>',
            phoneNumber: '1234567890',
            gender: 'Male',
        },
        {
            id: '2',
            fullName: '<PERSON>',
            phoneNumber: '0987654321',
            gender: 'Female',
        },
    ],
    addContact: (contact) =>
        set((state) => ({
            contacts: [...state.contacts, { ...contact, id: Date.now().toString() }],
        })),
    updateContact: (id, contact) =>
        set((state) => ({
            contacts: state.contacts.map((c) => (c.id === id ? { ...contact, id } : c)),
        })),
    removeContact: (id) =>
        set((state) => ({
            contacts: state.contacts.filter((c) => c.id !== id),
        })),
}));
