import * as Yup from 'yup';

export const emergencyContactSchema = Yup.object().shape({
    fullName: Yup.string().required('Full name is required')
        .min(3, 'Full name must be at least 3 characters')
        .max(35, 'Full name must be at maximum of 35 characters'),
    phoneNumber: Yup.string()
        .matches(/^[0-9]{10}$/, 'Enter a valid 10-digit phone number')
        .required('Phone number is required'),
    selectedGender: Yup.string().required('*Please select a relationship'),
});
