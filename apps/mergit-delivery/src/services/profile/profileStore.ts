import { create } from 'zustand';
import { ProfileDetails } from './profileTypes';

type Contact = {
    name: string;
    number: string;
    city?: string;
};

type ProfileState = {
    profileDetails: ProfileDetails | null;
    setProfileDetails: (details: ProfileDetails | null) => void;

    selectedContact: Contact | null;
    setSelectedContact: (contact: Contact) => void;
    resetContact: () => void;

    fullName: string;
    accountNumber: string;
    ifscCode: string;
    setBankDetails: (details: {
        fullName: string;
        accountNumber: string;
        ifscCode: string;
    }) => void;
};

export const useProfileStore = create<ProfileState>((set) => ({
    profileDetails: {
        partnerId: '1',
        name: '<PERSON>',
        phone: '**********',
    },
    setProfileDetails: (details) => set({ profileDetails: details }),

    selectedContact: null,
    setSelectedContact: (contact: Contact) => set({ selectedContact: contact }),
    resetContact: () => set({ selectedContact: null }),

    fullName: '<PERSON>',
    accountNumber: '**********',
    ifscCode: 'CNRB0016205',
    setBankDetails: (details) => set({ ...details }),
}));
