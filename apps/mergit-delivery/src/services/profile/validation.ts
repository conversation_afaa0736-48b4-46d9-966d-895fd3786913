import * as Yup from 'yup';

export const bankDetailsSchema = Yup.object().shape({
    fullName: Yup.string()
        .required('Full name is required')
        .min(3, 'Full name must be at least 3 characters')
        .max(50, 'Full name must not exceed 50 characters'),
    accountNumber: Yup.string()
        .matches(/^\d+$/, 'Account number must be numeric')
        .required('Account number is required')
        .min(6, 'Account number must be at least 6 digits')
        .max(18, 'Account number cannot exceed 18 digits'),
    reAccountNumber: Yup.string()
        .oneOf([Yup.ref('accountNumber')], 'Account numbers must match')
        .required('Please re-enter account number'),
    ifscCode: Yup.string()
        .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code')
        .required('IFSC code is required'),
});

export const referralSchema = Yup.object({
    contactName: Yup.string().required('Contact name is required'),
    contactNumber: Yup.string()
        .matches(/^[0-9]{10}$/, 'Enter a valid 10-digit number')
        .required('Contact number is required'),
    cityName: Yup.string().required('City name is required'),
});
