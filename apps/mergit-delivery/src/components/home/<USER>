import React, { useMemo } from 'react';
import { View, Text } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { useLocationStore } from '~/services/location';
import { useTranslation } from 'react-i18next';
import { useProfileStore } from 'services/profile';

const Greetings = () => {
    const { t } = useTranslation();

    const { isEnabled } = useLocationStore();
    const { profileDetails } = useProfileStore();

    const greetings = useMemo(() => {
        const date = new Date();
        const hours = date.getHours();

        if (hours < 12) {
            return t('goodMorning');
        } else if (hours < 18) {
            return t('goodAfternoon');
        } else {
            return t('goodEvening');
        }
    }, [t]);

    return (
        <Animated.View layout={LinearTransition.duration(600).damping(300)} className="gap-4">
            <Text style={fonts.fontSemiBold} className="text-text16 tracking-wide text-secondary">
                {greetings} {profileDetails?.name}!
            </Text>

            {isEnabled && (
                <View className="gap-[6px]">
                    <View className="flex-row items-center">
                        <Text
                            style={fonts.fontSemiBold}
                            className="text-text16 tracking-wide text-secondary">
                            {t('youAre')}&nbsp;
                        </Text>
                        <Text
                            style={fonts.fontSemiBold}
                            className="text-text16 tracking-wide text-success">
                            {t('online')}&nbsp;
                        </Text>
                        <Text
                            style={fonts.fontSemiBold}
                            className="text-text16 tracking-wide text-secondary">
                            {t('now')}
                        </Text>
                    </View>
                    <Text
                        style={fonts.fontRegular}
                        className="text-text15 tracking-wide text-neutral">
                        {t('youWillReceiveOrders')}
                    </Text>
                </View>
            )}
        </Animated.View>
    );
};

export default Greetings;
