import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { useEffect, useRef, useState } from 'react';
import { fonts } from '@mergit-mobile/styles';
import { rupeeSymbol } from 'utils/constants';
import { SwipeSwitch } from 'components/orders';
import { OrderStatusUpdate, useOrderStore } from 'services/orders';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useLocationStore } from '~/services/location';
import { Toast } from 'context/CustomToastProvider';
import { useBottomSheet } from '@mergit-mobile/components';

const REJECT_TIME = 30;

export const NewOrder = () => {
    const { t } = useTranslation();
    const [rejectTimer, setRejectTimer] = useState(REJECT_TIME);
    const { hideBottomSheet } = useBottomSheet();

    const {
        updatePreviousOrders,
        previousOrders,
        newSellers,
        updateCurrentRouteStop,
        orderAssigned,
    } = useOrderStore();
    const { deliveryPartnerId } = useLocationStore();

    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (!intervalRef.current) return;

        intervalRef.current = setInterval(() => {
            setRejectTimer((prevTimer) => {
                if (prevTimer === 1) {
                    rejectOrder();
                    return 0;
                }
                console.log(prevTimer);
                return prevTimer - 1;
            });
        }, 1000);

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            setRejectTimer(REJECT_TIME);
        };
    }, []);

    const rejectOrder = async () => {
        try {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            const ids = orderAssigned?.map((order: any) => ({
                orderId: order.orderId,
                subOrderId: order.subOrderId,
                userId: deliveryPartnerId,
                status: OrderStatusUpdate.REJECTED,
                rejectedOrderData: order,
            }));
            if (ids) {
                Toast.show({
                    message: 'Order denied',
                });
            }
            hideBottomSheet();
            setRejectTimer(REJECT_TIME);
        } catch (error) {
            console.log('Error update status in order rejection : ', error);
        } finally {
        }
    };

    const updateStates = async () => {
        try {
            if (!previousOrders) {
                router.replace('/order-pickup-location');
            }
        } catch (error) {
            console.log('Error in update app states : ', error);
        }
    };

    const acceptOrder = async () => {
        setLoading(true);
        try {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            updateCurrentRouteStop(0);
            setRejectTimer(REJECT_TIME);
            updatePreviousOrders(true);
            hideBottomSheet();
            updateStates();

        } catch (error) {
            console.log('Error in accept order : ', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <ScrollView
            contentContainerStyle={{ backgroundColor: '#fff' }}
            contentContainerClassName="flex-grow px-primary py-primary">
            <TouchableOpacity activeOpacity={1} className="gap-5">
                <View className="flex-row items-center justify-between">
                    <Text
                        className="text-text16 tracking-wide text-secondary"
                        style={fonts.fontSemiBold}>
                        {newSellers?.length} New Order
                    </Text>
                    <View className="rounded-full border border-[#F04438] bg-[#FEE4E2] px-3 py-[6px]">
                        <Text
                            style={fonts.fontMedium}
                            className="text-text14 tracking-wide text-[#B42318]">
                            {t('Reject in')} {rejectTimer}s
                        </Text>
                    </View>
                </View>

                <View className="items-center gap-3">
                    <Text
                        onPress={acceptOrder}
                        className="text-text16 tracking-wider text-secondary"
                        style={fonts.fontSemiBold}>
                        {t('Expected Earnings')}
                    </Text>

                    <Text
                        className="text-text18 tracking-wider text-secondary"
                        style={fonts.fontSemiBold}>
                        {rupeeSymbol}1200
                    </Text>
                    <Text
                        className="text-text14 tracking-wider text-neutral"
                        style={fonts.fontMedium}>
                        {t('Pickup')} : | {t('Delivery')} :
                    </Text>
                </View>

                <View className="gap-3">
                    {newSellers?.map((seller, index) => (
                        <View
                            className="w-full gap-[6px] rounded-lg border border-border px-4 py-4"
                            key={index}>
                            <Text
                                style={fonts.fontMedium}
                                className="text-text15 uppercase tracking-wide text-neutral">
                                {t('Pickup {{index}} From', {
                                    index: index + 1,
                                })}
                            </Text>

                            <Text
                                style={fonts.fontSemiBold}
                                className="text-text16 tracking-wider text-secondary">
                                {seller?.name}
                            </Text>
                            <Text
                                style={fonts.fontRegular}
                                className="text-text15 tracking-wide text-neutral"
                                numberOfLines={2}
                                ellipsizeMode="tail">
                                {seller?.address ?? '-- No address --'}
                            </Text>
                            <Text
                                style={fonts.fontRegular}
                                className="text-text15 tracking-wide text-neutral">
                                {seller?.preparingTime?.value} mins away
                            </Text>
                        </View>
                    ))}
                </View>

                <SwipeSwitch
                    title="Accept Order"
                    onToggle={acceptOrder}
                    loading={loading}
                    disabled={loading}
                />
            </TouchableOpacity>
        </ScrollView>
    );
};
