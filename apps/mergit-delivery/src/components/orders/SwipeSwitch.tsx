import { useEffect, useState } from 'react';
import { View, Text, PanResponder, StyleSheet, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    runOnJS,
} from 'react-native-reanimated';
import { fonts } from '@mergit-mobile/styles';
import { colors } from 'utils/constants';

type SwipeSwitchProps = {
    onToggle: (isOn: boolean) => void;
    title: string;
    disabled?: boolean;
    loading?: boolean;
};

const THUMB_WIDTH = 70;
const PADDING = 5;
const INITIAL_VALUE = false;

const SwipeSwitch = ({ onToggle, title, disabled = false, loading = false }: SwipeSwitchProps) => {
    const [containerWidth, setContainerWidth] = useState(0);
    const maxTranslate = containerWidth - THUMB_WIDTH - PADDING;

    const isOn = useSharedValue(INITIAL_VALUE);
    const translateX = useSharedValue(INITIAL_VALUE ? maxTranslate : PADDING);

    useEffect(() => {
        translateX.value = withTiming(INITIAL_VALUE ? maxTranslate : PADDING);
        isOn.value = INITIAL_VALUE;
    }, [INITIAL_VALUE, maxTranslate]);

    const panResponder = PanResponder.create({
        onStartShouldSetPanResponder: () => !disabled,
        onMoveShouldSetPanResponder: () => !disabled,
        onPanResponderMove: (_, gesture) => {
            if (disabled) return;
            translateX.value = Math.max(
                PADDING,
                Math.min(maxTranslate, gesture.dx + (isOn.value ? maxTranslate : PADDING))
            );
        },
        onPanResponderRelease: () => {
            if (disabled) return;
            const shouldTurnOn = translateX.value > containerWidth / 2;

            if (shouldTurnOn) {
                isOn.value = true;
                translateX.value = withTiming(maxTranslate, { duration: 200 });
                if (onToggle) {
                    runOnJS(onToggle)(true);
                    setTimeout(() => {
                        isOn.value = false;
                        translateX.value = withTiming(PADDING, {
                            duration: 200,
                        });
                    }, 5000);
                }
            } else {
                isOn.value = false;
                translateX.value = withTiming(PADDING, { duration: 200 });
            }
        },
    });

    const animatedThumbStyle = useAnimatedStyle(() => ({
        transform: [{ translateX: translateX.value }],
    }));

    return (
        <View onLayout={(e) => setContainerWidth(e.nativeEvent.layout.width)}>
            <Animated.View style={[styles.switch, disabled && { opacity: 0.6 }]}>
                <Animated.View
                    {...(!disabled ? panResponder.panHandlers : {})}
                    style={[styles.thumb, animatedThumbStyle]}>
                    {loading ? (
                        <ActivityIndicator size={20} color={colors.primary} />
                    ) : (
                        <MaterialIcons
                            name="keyboard-double-arrow-right"
                            size={28}
                            color={colors.primary}
                        />
                    )}
                </Animated.View>
                <Text className="text-text15 tracking-wider text-white" style={fonts.fontSemiBold}>
                    {title}
                </Text>
            </Animated.View>
        </View>
    );
};

const styles = StyleSheet.create({
    switch: {
        width: '100%',
        height: 60,
        borderRadius: 30,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: colors.primary,
    },
    thumb: {
        width: THUMB_WIDTH,
        height: 50,
        borderRadius: 30,
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        left: 0,
        top: PADDING,
        bottom: PADDING,
        backgroundColor: '#fff',
    },
});

export default SwipeSwitch;
