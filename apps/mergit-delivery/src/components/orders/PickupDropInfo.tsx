import { useCallback, useState } from 'react';
import { View, Text, Platform, Linking } from 'react-native';
import SwipeSwitch from './SwipeSwitch';
import { CustomBtn } from '@mergit-mobile/components';
import { fonts, shadow } from '@mergit-mobile/styles';
import { Feather, Ionicons } from '@expo/vector-icons';
import { OrderStatus, OrderStatusUpdate, useOrderStore } from 'services/orders';
import { colors } from 'utils/constants';
import { useLocationStore } from '~/services/location';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { CustomText } from 'components/common';
import { useTabsStore } from '~/services/tabs';

type CoordinatesType = {
    latitude: number;
    longitude: number;
};

type PickupDropInfoProps = {
    travelLocation: CoordinatesType | undefined;
    userType: 'seller' | 'customer';
    isButtonEnabled: boolean;
};

const PickupDropInfo = ({ travelLocation, userType, isButtonEnabled }: PickupDropInfoProps) => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const {
        currentRouteStop,
        orders,
        updateSellerOrdersIndex,
        sellerOrdersIndex,
        updateOrderPackageImages,
        updateUserOrdersIndex,
        userOrdersIndex,
    } = useOrderStore();
    const { deliveryPartnerLocation, deliveryPartnerId } = useLocationStore();

    const [loading, setLoading] = useState(false);

    const goToMap = useCallback(() => {
        if (deliveryPartnerLocation?.coords && travelLocation) {
            const { latitude: dLatitude, longitude: dLongitude } = travelLocation;
            const { latitude: sLatitude, longitude: sLongitude } = deliveryPartnerLocation.coords;

            const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${sLatitude},${sLongitude}&destination=${dLatitude},${dLongitude}&travelmode=driving`;
            const appleMapsUrl = `http://maps.apple.com/?saddr=${sLatitude},${sLongitude}&daddr=${dLatitude},${dLongitude}&dirflg=d`;

            const url = Platform.OS === 'ios' ? appleMapsUrl : googleMapsUrl;

            Linking.canOpenURL(url).then((supported) => {
                if (supported) {
                    Linking.openURL(url);
                } else {
                    console.log('Error', 'Your device cannot open Maps');
                }
            });
        }
    }, [deliveryPartnerLocation?.coords, travelLocation]);

    const makeCall = useCallback(() => {
        Linking.openURL(`tel:${orders?.routes[currentRouteStop]?.contact}`);
    }, [currentRouteStop, orders?.routes]);

    const updateStatus = async () => {
        setLoading(true);
        try {
            const ids = orders?.routes[currentRouteStop].orders?.map((order) => ({
                orderId: order.orderId,
                subOrderId: order.subOrderId,
                userId: deliveryPartnerId,
                status:
                    userType === 'seller'
                        ? OrderStatusUpdate.Reached_Pickup_Location
                        : OrderStatusUpdate.Reached_User_Location,
            }));

            if (ids?.length) {
                const status =
                    userType === 'seller'
                        ? OrderStatus.Reached_Pickup_Location
                        : OrderStatus.Reached_User_Location;
                ids.forEach((id) =>
                    updateOrderPackageImages({
                        subOrderId: id.subOrderId,
                        status,
                    })
                );

                if (userType === 'seller') {
                    const nextSellerOrderIndex = sellerOrdersIndex + 1;
                    updateStates(nextSellerOrderIndex, userOrdersIndex);
                } else {
                    const nextUserOrderIndex = userOrdersIndex + 1;
                    updateStates(sellerOrdersIndex, nextUserOrderIndex);
                }
            }
        } catch (error) {
            console.log(`Error update status in order ${userType} location : `, error);
        } finally {
            setLoading(false);
        }
    };

    const updateStates = async (sellerIndex: number, userIndex: number) => {
        try {
            if (userType === 'seller') {
                router.replace('/order-pickedup');
                updateSellerOrdersIndex(sellerIndex);
            } else {
                router.push('/order-deliver');
                updateUserOrdersIndex(userIndex);
            }

        } catch (error) {
            console.log('Error in update app states : ', error);
        }
    };

    return (
        <View className="gap-3 bg-white px-primary pt-3" style={[shadow, { paddingBottom: bottomInset }]}>
            <View className="gap-[6px]">
                {userType === 'seller' ? (
                    <View className="gap-[6px]">
                        {orders?.routes[currentRouteStop]?.orders?.map((currOrder, index) => (
                            <View className="flex-row items-center" key={index}>
                                <Text
                                    className="text-text13 tracking-wide text-secondary"
                                    style={fonts.fontMedium}>
                                    Order ID&nbsp;
                                </Text>
                                <Text
                                    className="text-text13 tracking-wide text-secondary"
                                    style={fonts.fontSemiBold}>
                                    #{currOrder.subOrderId}
                                </Text>
                            </View>
                        ))}
                    </View>
                ) : (
                    <CustomText
                        tKey="customer_location"
                        className="text-text13 tracking-wide text-neutral"
                        style={fonts.fontMedium}
                    />
                )}

                <Text
                    className="text-text16 tracking-wider text-secondary"
                    style={fonts.fontSemiBold}
                    numberOfLines={1}
                    ellipsizeMode="tail">
                    {orders?.routes[currentRouteStop]?.name}
                </Text>
                <Text
                    className="text-text15 tracking-wider text-secondary"
                    style={fonts.fontRegular}
                    ellipsizeMode="tail">
                    {orders?.routes[currentRouteStop]?.address ?? '-- No address --'}
                </Text>
            </View>

            <View className="h-px w-full bg-border" />

            <View className="flex-row items-center gap-3">
                <CustomBtn
                    title={t('call')}
                    onPress={makeCall}
                    backgroundColor="transparent"
                    borderColor={colors.primary}
                    textStyle={{ color: colors.primary }}
                    btnStyle={{ flex: 1 }}
                    icon={<Ionicons name="call-outline" size={22} color={colors.primary} />}
                    position="left"
                />

                <CustomBtn
                    title={t('go_to_map')}
                    onPress={goToMap}
                    btnStyle={{ flex: 1 }}
                    icon={<Feather name="navigation" size={22} color="#fff" />}
                    position="left"
                />
            </View>

            <SwipeSwitch
                title={userType === 'seller' ? 'Reached Pickup Location' : 'Reached Drop Location'}
                onToggle={updateStatus}
                disabled={isButtonEnabled || loading}
                loading={loading}
            />
        </View>
    );
};

export default PickupDropInfo;
