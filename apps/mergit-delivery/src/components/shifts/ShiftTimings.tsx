import { View, Text, Pressable } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { CheckBox, CustomBtn, useCustomModal } from '@mergit-mobile/components';
import { colors } from 'utils/constants';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { ShiftTimingsType } from '~/utils/mock';
import { useMemo, useState } from 'react';

type ShiftTab = 'All' | 'Opened' | 'Closed';

type ShiftTimingsProps = {
    selectedTab: ShiftTab;
    handleShiftSelection: (id: string) => void;
    shiftsData: ShiftTimingsType[];
    selectedShifts: string[];
};

export const ShiftTimings = ({
    selectedTab,
    handleShiftSelection,
    shiftsData,
    selectedShifts
}: ShiftTimingsProps) => {
    const { t } = useTranslation();
    const { showModal, hideModal } = useCustomModal();

    const handleCancelSlot = (id: string) => {
        showModal(
            <View className="gap-4">
                <Text
                    style={fonts.fontSemiBold}
                    className="text-text17 tracking-wider text-secondary">
                    {t('cancelSlot')}
                </Text>

                <Text style={fonts.fontRegular} className="text-text15 tracking-wide text-neutral">
                    {t('cancelSlotConfirmation')}
                </Text>

                <View className="mt-1 flex-row items-center gap-3">
                    <CustomBtn
                        title={t('cancel')}
                        onPress={hideModal}
                        height={50}
                        borderColor={colors.primary}
                        backgroundColor="transparent"
                        btnStyle={{ flex: 1 }}
                        textStyle={{ color: colors.primary }}
                    />

                    <CustomBtn
                        title={t('confirm')}
                        onPress={hideModal}
                        height={50}
                        btnStyle={{ flex: 1 }}
                    />
                </View>
            </View>
        );
    };

    const filteredShifts = useMemo(() => {
        if (selectedTab === 'All') {
            return shiftsData;
        } else if (selectedTab === 'Opened') {
            return shiftsData.map((shift) => ({
                ...shift,
                data: shift.data.filter((item) => !item.isBooked),
            }));
        } else if (selectedTab === 'Closed') {
            return shiftsData.map((shift) => ({
                ...shift,
                data: shift.data.filter((item) => item.isBooked),
            }));
        }
        return shiftsData;
    }, [selectedTab, shiftsData]);

    return (
        <View className="flex-1 gap-6">
            {filteredShifts.map((shift, index) => {
                if (shift.data.length === 0) {
                    return null;
                }
                return (
                    <View
                        className="w-full overflow-hidden rounded-xl border border-border"
                        key={index}>
                        <View className="rounded-t-lg bg-[#A9D8D8] px-4 py-primary">
                            <Text
                                style={fonts.fontSemiBold}
                                className="text-text16 tracking-wider text-secondary">
                                {shift.name}
                            </Text>
                        </View>
                        <View className="bg-white">
                            {shift.data.map((item, shiftIndex) => (
                                <View className="px-4" key={shiftIndex}>
                                    <Pressable
                                        onPress={item.isBooked ? undefined : () => handleShiftSelection(item.id)}
                                        className={`flex-row items-center gap-3 bg-white ${item.isBooked ? 'py-4' : 'py-primary'
                                            }`}>
                                        <Text
                                            style={fonts.fontMedium}
                                            className="text-text15 tracking-wider text-secondary">
                                            {item.title}
                                        </Text>
                                        <View className="flex-1 items-start">
                                            {item.isBooked && (
                                                <View className="rounded-full border border-[#17B26A] bg-[#DCFAE6] px-4 py-[6px]">
                                                    <Text
                                                        style={fonts.fontMedium}
                                                        className="text-text14 tracking-wide text-[#067647]">
                                                        {t('booked')}
                                                    </Text>
                                                </View>
                                            )}
                                        </View>
                                        {item.isBooked ? (
                                            <Pressable
                                                onPress={() => handleCancelSlot(item.id)}>
                                                <Ionicons
                                                    name="close"
                                                    size={24}
                                                    color={colors.secondary}
                                                />
                                            </Pressable>
                                        ) : (
                                            <CheckBox
                                                initialChecked={selectedShifts.includes(item.id)}
                                                onChange={() => handleShiftSelection(item.id)}
                                                checkboxSize={22}
                                                iconSize={16}
                                                borderColor={colors.neutral}
                                                checkedColor={colors.primary}
                                                checkboxColor="transparent"
                                            />
                                        )}
                                    </Pressable>
                                    {shiftIndex < shift.data.length - 1 && (
                                        <View className="h-px w-full bg-border" />
                                    )}
                                </View>
                            ))}
                        </View>
                    </View>
                )
            })}
        </View>
    );
};
