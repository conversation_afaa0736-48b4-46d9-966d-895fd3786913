import { View, Text, Modal, Pressable } from 'react-native';
import DatePicker from 'react-native-ui-datepicker';
import dayjs from 'dayjs';
import { fonts } from '@mergit-mobile/styles';
import { useTranslation } from 'react-i18next';

const Calendar = ({
    visible,
    setVisible,
    onConfirm,
    date,
    setDate,
}: {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    onConfirm: (date: string) => void;
    date: dayjs.Dayjs;
    setDate: (date: dayjs.Dayjs) => void;
}) => {
    const { t } = useTranslation();

    return (
        <Modal
            visible={visible}
            animationType="fade"
            transparent
            navigationBarTranslucent
            statusBarTranslucent
            onRequestClose={() => setVisible(false)}
            onDismiss={() => setVisible(false)}>
            <View className="flex-1 items-center justify-center bg-black/50 px-4">
                <View className="max-w-lg gap-3 rounded-xl bg-white p-primary">

                    <Text style={fonts.fontBold} className="tracking-wide text-text18 text-secondary">
                        {dayjs(date).format('ddd, MMM D')}
                    </Text>

                    <DatePicker
                        mode="single"
                        date={date.toDate()}
                        onChange={(params) => {
                            setDate(dayjs(params.date));
                        }}
                        locale="en"
                        className="gap-6"
                        maxDate={dayjs().toDate()}
                        navigationPosition="right"
                        styles={{
                            today: { borderColor: 'transparent' },
                            selected: {
                                backgroundColor: '#1F7575',
                                borderRadius: 25,
                                width: 47,
                                alignSelf: 'center',
                            },
                            selected_label: {
                                color: 'white',
                                fontFamily: 'DMSans-SemiBold',
                            },
                            weekday_label: {
                                fontFamily: 'DMSans-Regular',
                                color: '#0A0D12',
                                fontSize: 16,
                            },
                            day_label: {
                                fontFamily: 'DMSans-Regular',
                                color: '#0A0D12',
                                fontSize: 16,
                            },
                            disabled_label: {
                                color: '#0A0D12',
                                opacity: 0.3,
                                fontFamily: 'DMSans-Regular',
                                fontSize: 16,
                            },
                            month_label: {
                                fontFamily: 'DMSans-Regular',
                                color: '#0A0D12',
                                fontSize: 16,
                            },
                            selected_month_label: {
                                fontFamily: 'DMSans-SemiBold',
                                color: '#fff',
                                fontSize: 16,
                            },
                            selected_month: {
                                backgroundColor: '#1F7575',
                                borderRadius: 25,
                            },
                            month_selector_label: {
                                fontFamily: 'DMSans-SemiBold',
                                color: '#717680',
                                fontSize: 16,
                            },
                            year_selector_label: {
                                fontFamily: 'DMSans-SemiBold',
                                color: '#717680',
                                fontSize: 16,
                            },
                            year_label: {
                                fontFamily: 'DMSans-Regular',
                                color: '#0A0D12',
                                fontSize: 16,
                            },
                            selected_year_label: {
                                fontFamily: 'DMSans-SemiBold',
                                color: '#fff',
                                fontSize: 16,
                            },
                            selected_year: {
                                backgroundColor: '#1F7575',
                                borderRadius: 25,
                            },
                            weekdays: { marginBottom: 10 },
                            button_next_image: { tintColor: '#0A0D12' },
                            button_prev_image: { tintColor: '#0A0D12' },
                        }}
                    />

                    <View className="w-full flex-row items-center justify-end gap-10">
                        <Pressable
                            onPress={() => setVisible(false)}>
                            <Text
                                style={fonts.fontSemiBold}
                                className="text-neutral text-text15 tracking-wider">
                                {t('cancel')}
                            </Text>
                        </Pressable>
                        <Pressable
                            onPress={() => {
                                onConfirm(date?.toString());
                                setVisible(false);
                            }}>
                            <Text
                                style={fonts.fontSemiBold}
                                className="text-primary text-text15 tracking-wider">
                                {t('confirm')}
                            </Text>
                        </Pressable>
                    </View>
                </View>
            </View>
        </Modal>
    )
};

export default Calendar;
