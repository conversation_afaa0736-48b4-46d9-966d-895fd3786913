import { View, Text, Pressable } from 'react-native';
import React from 'react';
import { fonts } from '@mergit-mobile/styles';
import { router } from 'expo-router';
import { useProfileStore } from 'services/profile';
import { useTranslation } from 'react-i18next';

const BankDetails = () => {
    const { fullName, accountNumber, ifscCode } = useProfileStore();
    const { t } = useTranslation();

    return (
        <View>
            <View className="mb-3 mt-4 flex-row items-center justify-between">
                <Text className="text-xl" style={fonts.fontBold}>
                    {t('bank_details')}
                </Text>

                <Pressable onPress={() => router.push('/profile/edit-bank-details')}>
                    <Text className="text-text16 text-primary underline" style={fonts.fontBold}>
                        {t('edit')}
                    </Text>
                </Pressable>
            </View>

            <View className="rounded-lg bg-white p-5">
                <View className="mb-5 flex-row items-center justify-between border-b border-gray-200 pb-5">
                    <Text className="text-text15 text-[#0A0D12]" style={fonts.fontRegular}>
                        {t('fullName')}
                    </Text>
                    <Text className="text-text15 text-[#0A0D12]" style={fonts.fontSemiBold}>
                        {fullName}
                    </Text>
                </View>
                <View className="mb-5 flex-row items-center justify-between border-b border-gray-200 pb-5">
                    <Text className="text-text15 text-[#0A0D12]" style={fonts.fontRegular}>
                        {t('accountNumber')}
                    </Text>
                    <Text className="text-text15 text-[#0A0D12]" style={fonts.fontSemiBold}>
                        {accountNumber}
                    </Text>
                </View>
                <View className="flex-row items-center justify-between">
                    <Text className="text-text15 text-[#0A0D12]" style={fonts.fontRegular}>
                        {t('ifscCode')}
                    </Text>
                    <Text className="text-text15 text-[#0A0D12]" style={fonts.fontSemiBold}>
                        {ifscCode}
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default BankDetails;
