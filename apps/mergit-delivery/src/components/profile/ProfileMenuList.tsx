import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { FontAwesome6 } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useBottomSheet } from '@mergit-mobile/components';
import LanguageBottomSheet from './LanguageBottomSheet';

const ProfileMenuList = () => {
    const { showBottomSheet } = useBottomSheet();
    const { t } = useTranslation();

    const menuList = [
        {
            id: 1,
            title: t('myProfile'),
            onPress: () => router.push('/(protected)/profile/(my-profile)'),
        },
        {
            id: 2,
            title: t('orderHistory'),
            onPress: () => router.push('/orders-history'),
        },
        {
            id: 3,
            title: t('wallet'),
            onPress: () => router.push('/(protected)/(tabs)/money')
        },
        {
            id: 4,
            title: t('earnings'),
            onPress: () => router.push('/earnings'),
        },
        {
            id: 5,
            title: t('incentives'),
            onPress: () => router.push('/(protected)/(incentive)'),
        },
        {
            id: 6,
            title: t('helpSupport'),
            onPress: () => router.push('/(protected)/profile/(help-and-support)'),
        },
        {
            id: 7,
            title: t('myTickets'),
            onPress: () => router.push('/(protected)/profile/(my-tickets)'),
        },
        {
            id: 8,
            title: t('appLanguage'),
            onPress: () => showBottomSheet(() => <LanguageBottomSheet />, 'hide')
        },
        {
            id: 9,
            title: t('trainingVideos'),
            onPress: () => router.push('/(protected)/profile/(training-videos)'),
        },
        {
            id: 10,
            title: t('referFriend'),
            onPress: () => router.push('/(protected)/profile/(referrals)'),
        },
    ];

    return (
        <View className="w-full rounded-xl bg-white px-5">
            <FlatList
                data={menuList}
                renderItem={({ item, index }) => (
                    <Animated.View
                        entering={FadeInUp.duration(800)
                            .delay(100 * index)
                            .springify()}>
                        <TouchableOpacity
                            onPress={item.onPress}
                            activeOpacity={0.8}
                            className="flex-row items-center py-6">
                            <Text
                                style={fonts.fontMedium}
                                className="flex-1 text-text16 tracking-wide text-secondary">
                                {item.title}
                            </Text>
                            <FontAwesome6 name="angle-right" size={16} color="#1F7575" />
                        </TouchableOpacity>
                    </Animated.View>
                )}
                ItemSeparatorComponent={() => <View className="h-px w-full bg-border" />}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
            />
        </View>
    );
};

export default ProfileMenuList;
