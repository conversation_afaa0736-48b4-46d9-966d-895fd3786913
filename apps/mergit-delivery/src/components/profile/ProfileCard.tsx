import { View, Text } from 'react-native';
import { Image } from 'expo-image';
import { fonts } from '@mergit-mobile/styles';
import { useProfileStore } from 'services/profile';

const PROFILE_IMG_SIZE = 80;

const ProfileCard = () => {
    const { profileDetails } = useProfileStore();

    return (
        <View className="flex-row items-center gap-3 rounded-xl bg-white px-4 py-primary">
            <Image
                source={{ uri: `https://picsum.photos/id/27/200/300` }}
                style={{
                    height: PROFILE_IMG_SIZE,
                    width: PROFILE_IMG_SIZE,
                    borderRadius: PROFILE_IMG_SIZE / 2,
                }}
                contentFit="cover"
                transition={200}
                cachePolicy="memory-disk"
            />
            <View className="gap-[6px]">
                <Text
                    className="text-text16 tracking-wide text-secondary"
                    style={fonts.fontSemiBold}>
                    {profileDetails?.name}
                </Text>

                <Text className="text-text15 tracking-wide text-secondary" style={fonts.fontMedium}>
                    +91 {profileDetails?.phone}
                </Text>

                <Text
                    className="text-text15 tracking-wide text-secondary"
                    style={fonts.fontMedium}
                    numberOfLines={1}>
                    <EMAIL>
                </Text>
            </View>
        </View>
    );
};

export default ProfileCard;
