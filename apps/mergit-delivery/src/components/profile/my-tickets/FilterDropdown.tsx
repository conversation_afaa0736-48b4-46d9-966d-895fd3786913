import { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { <PERSON><PERSON><PERSON>, Feather, Ionicons } from '@expo/vector-icons';
import { fonts } from '@mergit-mobile/styles';
import { useBottomSheet } from '@mergit-mobile/components';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { useTabsStore } from '~/services/tabs';

const weekOptions = Array.from({ length: 6 }, (_, i) => {
    const start = dayjs().subtract(i, 'week').startOf('week');
    const end = dayjs().subtract(i, 'week').endOf('week');
    return `${start.format('D MMM')} - ${end.format('D MMM')}`;
});

const FilterDropdown = () => {
    const [selectedFilter, setSelectedFilter] = useState('all');
    const { t } = useTranslation();

    const { showBottomSheet, hideBottomSheet } = useBottomSheet();
    const { bottomInset } = useTabsStore();

    const [selectedDateRange, setSelectedDateRange] = useState(weekOptions[0]);

    const radioOptions = [
        { id: 1, label: t('all_tickets'), value: 'all' },
        { id: 2, label: t('open_tickets'), value: 'open' },
        { id: 3, label: t('closed_tickets'), value: 'closed' },
    ];

    const handleDateRangeChange = (filter: string) => {
        setSelectedDateRange(filter);
        hideBottomSheet();
    };

    const openSheet = (type: 'date' | 'filter') => {
        if (type === 'date') {
            showBottomSheet(() => (
                <View style={{ paddingBottom: bottomInset }}>
                    <View className="flex-row items-center justify-between p-primary">
                        <Text className="text-text16 tracking-wider text-secondary" style={fonts.fontSemiBold}>{t('select_week')}</Text>
                        <TouchableOpacity
                            className="h-10 w-10 items-center justify-center rounded-lg border border-primary"
                            activeOpacity={0.8}
                            onPress={hideBottomSheet}>
                            <Ionicons
                                name="close"
                                size={20}
                                color="#1F7575"
                            />
                        </TouchableOpacity>
                    </View>
                    <View className="h-px w-full bg-border" />
                    {weekOptions.map((range, index) => (
                        <TouchableOpacity
                            key={index}
                            onPress={() => handleDateRangeChange(range)}
                            className={`flex-row items-center gap-4 px-primary pt-primary ${index !== weekOptions.length - 1 ? 'border-b border-border pb-primary' : ''
                                }`}
                            activeOpacity={0.8}>
                            <Text
                                style={fonts.fontMedium}
                                className={`flex-1 tracking-wider text-text16 ${selectedDateRange === range
                                    ? 'text-primary'
                                    : 'text-secondary'
                                    }`}>
                                {range}
                            </Text>
                            {index === 0 && (
                                <View className="rounded-lg bg-primary px-3 py-2">
                                    <Text style={fonts.fontMedium} className="text-text14 text-white tracking-wide">This Week</Text>
                                </View>
                            )}
                        </TouchableOpacity>
                    ))}
                </View>
            ), 'hide');

        } else {
            showBottomSheet(() => (
                <View className='px-primary pt-primary' style={{ paddingBottom: bottomInset }}>

                    <View className="flex-row items-center gap-4 pb-primary">
                        <Text className="flex-1 text-text16 text-secondary tracking-wider" style={fonts.fontSemiBold}>
                            {t('filter_by_status')}
                        </Text>
                        <TouchableOpacity
                            activeOpacity={0.9}
                            onPress={hideBottomSheet}
                            className="h-10 w-10 justify-center items-center rounded-lg border border-primary">
                            <Ionicons
                                name="close"
                                size={22}
                                color="#1F7575"
                            />
                        </TouchableOpacity>
                    </View>

                    <View className="h-px w-full bg-border" />

                    <View>
                        {radioOptions.map((option) => {
                            const isSelected = selectedFilter === option.value;
                            return (
                                <TouchableOpacity
                                    key={option.id}
                                    className={`flex-row items-center py-5 gap-4 ${option.id !== radioOptions[radioOptions.length - 1].id
                                        ? 'border-b border-border'
                                        : ''
                                        }`}
                                    onPress={() => {
                                        setSelectedFilter(option.value);
                                        hideBottomSheet();
                                    }}
                                    activeOpacity={0.9}>
                                    <View
                                        className={`h-6 w-6 rounded-full border-2 ${isSelected ? 'border-primary' : 'border-black'
                                            } items-center justify-center`}>
                                        {isSelected && (
                                            <View className="h-3.5 w-3.5 rounded-full bg-primary" />
                                        )}
                                    </View>
                                    <Text className="flex-1 text-text16 tracking-wide text-secondary" style={fonts.fontMedium}>
                                        {option.label}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                    </View>
                </View>
            ), 'hide')
        }
    };

    return (
        <View className="mt-5 flex-row gap-3 bg-white px-primary">
            <TouchableOpacity
                className="min-h-[50px] flex-1 flex-row items-center gap-2 rounded-md border border-border px-2 py-4"
                onPress={() => openSheet('date')}
                activeOpacity={0.9}>
                <Feather name="calendar" size={20} color="black" />
                <Text
                    className="flex-1 text-text15 text-secondary tracking-wider"
                    style={fonts.fontMedium}
                    numberOfLines={1}
                    ellipsizeMode="tail">
                    {selectedDateRange}
                </Text>
                <Entypo
                    name="chevron-small-down"
                    size={24}
                    color="black"
                    style={{ marginLeft: 'auto' }}
                />
            </TouchableOpacity>

            <TouchableOpacity
                className="min-h-[50px] flex-1 flex-row items-center justify-between rounded-md border border-border px-4 py-4"
                onPress={() => openSheet('filter')}
                activeOpacity={0.9}>
                <Text className="flex-1 text-text15 text-secondary tracking-wider" style={fonts.fontMedium}>
                    {radioOptions.find((opt) => opt.value === selectedFilter)?.label ??
                        'All Tickets'}
                </Text>
                <Entypo name="chevron-small-down" size={24} color="black" />
            </TouchableOpacity>
        </View>
    );
};

export default FilterDropdown;
