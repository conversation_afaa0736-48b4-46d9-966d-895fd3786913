import { View, Text, SafeAreaView, TouchableOpacity, ScrollView, FlatList } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { Badge } from 'components/incentives';
import { Feather } from '@expo/vector-icons';
import { CustomBtn } from '@mergit-mobile/components';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const tickets = [
    {
        id: '67235',
        title: 'Pickup Issues',
        description:
            'The delivery partner, <PERSON>, arrived at the restaurant “Tasty Bites” at 10:30 AM for pickup of Order #45892, which was scheduled to be ready by 10:15 AM. Upon arrival, the restaurant staff informed the delivery partner that the order was not yet prepared, and they needed an additional 15 minutes to finish preparing the food. This caused a delay in the overall delivery schedule and impacted the partner’s ability to fulfill other scheduled deliveries. The partner waited for 15 minutes (until 10:45 AM), but the order still wasn’t ready, leading to a loss of valuable time.',
    },
    {
        id: '96347',
        title: 'Delivery Delay',
        description:
            'The delivery partner, <PERSON>, arrived at the restaurant “Tasty Bites” at 10:30 AM for pickup of Order #45892, which was scheduled to be ready by 10:15 AM. Upon arrival, the restaurant staff informed the delivery partner that the order was not yet prepared, and they needed an additional 15 minutes to finish preparing the food. This caused a delay in the overall delivery schedule and impacted the partner’s ability to fulfill other scheduled deliveries. The partner waited for 15 minutes (until 10:45 AM), but the order still wasn’t ready, leading to a loss of valuable time.',
    },
];

const TicketList = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1 px-primary">
            <FlatList
                data={tickets}
                keyExtractor={(item) => item.id}
                renderItem={({ item: ticket }) => (
                    <TouchableOpacity
                        onPress={() =>
                            router.push({
                                pathname: '/profile/particular-ticket',
                                params: {
                                    id: ticket.id,
                                    title: ticket.title,
                                    description: ticket.description,
                                },
                            })
                        }
                        activeOpacity={0.9}
                        key={ticket.id}
                        className="rounded-lg border border-border bg-white p-primary">
                        <Text className="text-text16 tracking-wide text-secondary" style={fonts.fontRegular}>
                            {t('ticket_id')}: #{ticket.id}
                        </Text>
                        <Text className="text-text15 leading-8 text-secondary tracking-wide" style={fonts.fontMedium}>
                            {ticket.title}
                        </Text>

                        <View className="flex-row items-center justify-between pt-3">
                            <Badge status="resolved" />
                            <Feather name="chevron-right" size={20} color="#1F7575" />
                        </View>
                    </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
                contentContainerClassName="flex-grow gap-4 pt-primary"
            />

            <View style={{ paddingBottom: bottomInset }}>
                <CustomBtn
                    title={t('addNewTicket')}
                    onPress={() => router.push('/(protected)/profile/(help-and-support)')}
                    height={50}
                />
            </View>
        </View>
    );
};

export default TicketList;
