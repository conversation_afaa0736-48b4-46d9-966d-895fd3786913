import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import PlayButton from 'assets/icons/training-video/PlayButton';
import { fonts } from '@mergit-mobile/styles';
import { Image } from 'expo-image';

type Video = {
    id: string;
    title: string;
    videourl: string;
    thumbnail?: string;
};

interface VideoSectionProps {
    title: string;
    videos: Video[];
    onPress: (videoUrl: string) => void;
}

const VideoSection: React.FC<VideoSectionProps> = ({ title, videos, onPress }) => {
    return (
        <View className='gap-3'>
            <Text className="px-primary text-text17 tracking-wide text-secondary" style={fonts.fontSemiBold}>
                {title}
            </Text>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerClassName='flex-grow gap-3 px-primary'>
                {videos.map((video) => (
                    <TouchableOpacity
                        key={video.id}
                        testID="video-item"
                        style={styles.card}
                        onPress={() => onPress(video.videourl)}
                        activeOpacity={0.9}>
                        <View className='h-[85px] w-[120px] relative rounded-lg'>
                            <Image
                                source={{
                                    uri: video.thumbnail || '',
                                }}
                                style={{ width: '100%', height: '100%', borderRadius: 8 }}
                                contentFit="cover"
                                transition={200}
                                cachePolicy="memory-disk"
                            />
                            <PlayButton style={styles.playIcon} />
                        </View>
                        <Text className='flex-1 text-text15 tracking-wide text-secondary' style={fonts.fontSemiBold}>{video.title}</Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </View>
    );
};

export default VideoSection;

const styles = StyleSheet.create({
    card: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,
        borderRadius: 10,
        borderWidth: 0.5,
        borderColor: '#D5D7DA',
        width: 350,
        gap: 10,
    },
    playIcon: {
        position: 'absolute',
        width: 30,
        height: 30,
        top: '50%',
        left: '50%',
        transform: [{ translateX: -15 }, { translateY: -15 }],
    },
});
