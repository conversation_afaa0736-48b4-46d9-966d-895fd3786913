import { ScrollView, View } from 'react-native';
import { videoList } from 'utils/mock';
import VideoSection from './VideoSection';
import { useTabsStore } from '~/services/tabs';

interface VideosListProps {
    handlePress: (video: string) => void;
}

const VideosList = ({ handlePress }: VideosListProps) => {
    const { bottomInset } = useTabsStore();

    return (
        <ScrollView contentContainerClassName='flex-grow gap-8 pt-primary' contentContainerStyle={{ paddingBottom: bottomInset }} showsVerticalScrollIndicator={false}>
            <VideoSection title="Live Order" videos={videoList.liveOrder} onPress={handlePress} />
            <VideoSection
                title="Support Ticket"
                videos={videoList.supportTicket}
                onPress={handlePress}
            />
            <VideoSection title="Earn More" videos={videoList.earnMore} onPress={handlePress} />
        </ScrollView>
    );
};

export default VideosList;
