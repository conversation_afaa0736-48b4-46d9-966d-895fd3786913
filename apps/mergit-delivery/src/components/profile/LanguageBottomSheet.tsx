import { useState } from 'react';
import { View, Text, Pressable, TouchableOpacity } from 'react-native';
import { Feather, Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { fonts } from '@mergit-mobile/styles';
import { CustomBtn, useBottomSheet } from '@mergit-mobile/components';
import { LanguageType, useAuthStore } from '~/services/auth';
import { useTabsStore } from '~/services/tabs';

const languages = [
    { name: 'English', code: 'en', value: `<PERSON><PERSON>'s language` },
    { name: 'Hindi', code: 'hi', value: 'हिन्दी' },
    { name: 'Tamil', code: 'ta', value: 'தமிழ்' },
];

const LanguageBottomSheet = () => {
    const { languageSelected, setLanguageSelected } = useAuthStore();
    const { i18n } = useTranslation();
    const { hideBottomSheet } = useBottomSheet();
    const { bottomInset } = useTabsStore();

    const [selectedLang, setSelectedLang] = useState(languageSelected);

    const handleLanguageChange = (languageCode: LanguageType) => {
        i18n.changeLanguage(languageCode);
        setLanguageSelected(languageCode);
    };

    return (
        <View className="gap-4 rounded-xl bg-white px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
            <View className="flex-row items-center justify-between">
                <View className="flex-row items-center gap-4">
                    <Ionicons
                        name="language"
                        size={28}
                        color="#fff"
                        style={{
                            backgroundColor: '#1F7575',
                            padding: 8,
                            borderRadius: 25,
                        }}
                    />
                    <Text style={fonts.fontSemiBold} className="text-lg text-primary">
                        App Language
                    </Text>
                </View>
                <Pressable
                    className="h-10 w-10 items-center justify-center rounded-lg border border-primary"
                    onPress={hideBottomSheet}>
                    <Feather name="x" size={24} color="#1F7575" />
                </Pressable>
            </View>

            <Text style={fonts.fontMedium} className="text-text15 text-grayText">
                Change your app language based on your preference
            </Text>

            <View className="h-px bg-border" />

            <View className="gap-4">
                {languages.map((language, index) => (
                    <TouchableOpacity
                        key={index}
                        onPress={() => setSelectedLang(language.code as LanguageType)}
                        activeOpacity={0.8}
                        className="flex-row items-center justify-between">
                        <View className="gap-2">
                            <Text
                                style={fonts.fontSemiBold}
                                className="text-text14 tracking-wide text-secondary">
                                {language.name}
                            </Text>
                            <Text
                                style={fonts.fontRegular}
                                className="text-text14 tracking-wide text-grayText">
                                {language.value}
                            </Text>
                        </View>

                        <View className="h-6 w-6 items-center justify-center rounded-full border border-primary">
                            {selectedLang === language.code && (
                                <View
                                    style={{
                                        height: 12,
                                        width: 12,
                                        borderRadius: 6,
                                        backgroundColor: '#1F7575',
                                    }}
                                />
                            )}
                        </View>
                    </TouchableOpacity>
                ))}
            </View>

            <CustomBtn
                title="Confirm"
                onPress={() => {
                    handleLanguageChange(selectedLang as LanguageType);
                    hideBottomSheet();
                }}
                height={50}
            />
        </View>
    );
};

export default LanguageBottomSheet;
