import { View, Text, Pressable, LayoutChangeEvent } from 'react-native';
import React, { useState, useEffect } from 'react';
import { fonts, shadow } from '@mergit-mobile/styles';
import { MaterialIcons } from '@expo/vector-icons';
import { rupeeSymbol } from 'utils/constants';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { router } from 'expo-router';
import Badge from './Badge';
import { useTranslation } from 'react-i18next';

const PROGRESS_COUNT = 20;
const COMPLETED_ORDERS = 3;

type IncentiveCardProps = {
    status?: 'open' | 'completed' | 'expired';
    showChevron?: boolean;
};

const IncentiveCard = ({ status = 'open', showChevron = true }: IncentiveCardProps) => {
    const [progressBarWidth, setProgressBarWidth] = useState(0);
    const { t } = useTranslation();

    const perOrderWidth = useSharedValue(0);
    const leftValue = useSharedValue(0);

    const handleLayout = (e: LayoutChangeEvent) => {
        const width = e.nativeEvent.layout.width;
        setProgressBarWidth(width);
        const perOrder = width / PROGRESS_COUNT;
        perOrderWidth.value = perOrder;
        leftValue.value = perOrder * COMPLETED_ORDERS;
    };

    useEffect(() => {
        if (progressBarWidth > 0) {
            leftValue.value = perOrderWidth.value * COMPLETED_ORDERS;
        }
    }, [progressBarWidth]);

    const animatedProgressBar = useAnimatedStyle(() => ({
        width: withTiming(perOrderWidth.value * COMPLETED_ORDERS, {
            duration: 500,
        }),
    }));

    const animatedDot = useAnimatedStyle(() => ({
        left: withTiming(perOrderWidth.value * COMPLETED_ORDERS, {
            duration: 500,
        }),
    }));

    return (
        <View className="gap-5 rounded-xl border border-border bg-white p-primary">
            <View className="gap-1">
                <Pressable className="flex-row items-center gap-2">
                    <Text
                        style={fonts.fontRegular}
                        className="flex-1 text-text16 tracking-wider text-secondary">
                        {t('completeOrdersToEarn', { count: PROGRESS_COUNT })}
                    </Text>

                    {showChevron && (
                        <Pressable
                            onPress={() => {
                                router.push('/(protected)/(incentive)');
                            }}>
                            <MaterialIcons name="keyboard-arrow-right" size={24} color="black" />
                        </Pressable>
                    )}
                </Pressable>

                <Text
                    style={fonts.fontSemiBold}
                    className="text-text16 tracking-wider text-secondary">
                    {rupeeSymbol}2000 {t('joining incentive')}
                </Text>
            </View>

            <View>
                <View
                    className="relative h-[6px] w-full justify-center rounded-full bg-border"
                    onLayout={handleLayout}>
                    <Animated.View
                        className="h-full rounded-l-full bg-primary"
                        style={animatedProgressBar}
                    />
                    <Animated.View
                        className="absolute h-6 w-6 rounded-full border border-white bg-primary"
                        style={[shadow, animatedDot]}
                    />
                </View>
            </View>

            {status === 'open' ? (
                <Text
                    style={fonts.fontMedium}
                    className="flex-1 text-text16 tracking-wider text-secondary">
                    7 {t('daysLeft')}
                </Text>
            ) : (
                <Badge status={status} />
            )}

            <View className="h-px w-full bg-border" />

            <View className="flex-row items-center gap-4">
                <View className="flex-1 items-center gap-1">
                    <Text
                        style={fonts.fontSemiBold}
                        className="flex-1 text-text16 tracking-wider text-secondary">
                        {COMPLETED_ORDERS} of {PROGRESS_COUNT}
                    </Text>
                    <Text
                        style={fonts.fontRegular}
                        className="flex-1 text-text14 tracking-wider text-secondary">
                        {t('Orders')}
                    </Text>
                </View>
                <View className="h-full w-px bg-border" />
                <View className="flex-1 items-center gap-1">
                    <Text
                        style={fonts.fontSemiBold}
                        className="flex-1 text-text16 tracking-wider text-secondary">
                        0:00 hrs
                    </Text>
                    <Text
                        style={fonts.fontRegular}
                        className="flex-1 text-text14 tracking-wider text-secondary">
                        {t('totalLoginHours')}
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default IncentiveCard;
