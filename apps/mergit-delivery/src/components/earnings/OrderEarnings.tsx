import { View, Text } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { rupeeSymbol } from 'utils/constants';
import { useTranslation } from 'react-i18next';

const OrderEarnings = () => {
    const { t } = useTranslation();
    return (
        <View className="flex-row items-center gap-4 rounded-lg border border-border bg-white px-4 py-5">
            <Text style={fonts.fontRegular} className="flex-1 text-text16 text-secondary">
                {t('order_earnings')}
            </Text>
            <Text style={fonts.fontSemiBold} className="text-text15 tracking-wide text-secondary">
                {rupeeSymbol}0
            </Text>
        </View>
    );
};

export default OrderEarnings;
