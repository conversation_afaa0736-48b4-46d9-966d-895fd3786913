import { Ionicons } from '@expo/vector-icons';
import { useBottomSheet } from '@mergit-mobile/components';
import { fonts } from '@mergit-mobile/styles';
import { View, Text, TouchableOpacity } from 'react-native'
import { useTabsStore } from '~/services/tabs';

type FilterType = 'daily' | 'weekly' | 'monthly';
type SheetType = 'weekly' | 'monthly';

type FilterOptionsProps = {
    type: SheetType;
    data: string[];
    selected: string;
    handleDateRangeChange: (range: string, filter: FilterType) => void;
};

const FilterOptions = ({ type, data, selected, handleDateRangeChange }: FilterOptionsProps) => {
    const { bottomInset } = useTabsStore();
    const { hideBottomSheet } = useBottomSheet();

    return (
        <View style={{ paddingBottom: bottomInset }}>
            <View className="flex-row items-center justify-between p-primary">
                <Text className="text-text16 tracking-wider text-secondary" style={fonts.fontSemiBold}>Select {type === 'weekly' ? 'Week' : 'Month'}</Text>
                <TouchableOpacity
                    className="h-10 w-10 items-center justify-center rounded-lg border border-primary"
                    activeOpacity={0.8}
                    onPress={hideBottomSheet}>
                    <Ionicons
                        name="close"
                        size={20}
                        color="#1F7575"
                    />
                </TouchableOpacity>
            </View>
            <View className="h-px w-full bg-border" />
            {data.map((range, index) => (
                <TouchableOpacity
                    key={index}
                    onPress={() => handleDateRangeChange(range, type)}
                    className={`flex-row items-center gap-4 px-primary pt-primary ${index !== data.length - 1 ? 'border-b border-border pb-primary' : ''
                        }`}
                    activeOpacity={0.8}>
                    <Text
                        style={fonts.fontMedium}
                        className={`flex-1 tracking-wider text-text16 ${selected === range
                            ? 'text-primary'
                            : 'text-secondary'
                            }`}>
                        {range}
                    </Text>
                    {index === 0 && (
                        <View className="rounded-lg bg-primary px-3 py-2">
                            <Text style={fonts.fontMedium} className="text-text14 text-white tracking-wide">This {type === 'weekly' ? 'Week' : 'Month'}</Text>
                        </View>
                    )}
                </TouchableOpacity>
            ))}
        </View>
    )
}

export default FilterOptions