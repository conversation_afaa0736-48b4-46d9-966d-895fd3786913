import { View, Text, TouchableOpacity } from 'react-native';
import { fonts } from '@mergit-mobile/styles';

type FilterType = 'daily' | 'weekly' | 'monthly';

interface FilterTabProps {
    selectedFilter: FilterType;
    handleFilterSelection: (filter: FilterType) => void;
}

const FilterTab = ({ selectedFilter, handleFilterSelection }: FilterTabProps) => {

    const handleFilterChange = (filter: FilterType) => {
        handleFilterSelection(filter);
    };

    return (
        <View className="flex-row items-center gap-5">
            {['daily', 'weekly', 'monthly'].map((filter) => (
                <TouchableOpacity
                    key={filter}
                    className={`rounded-lg border border-border px-6 py-3 ${selectedFilter === filter ? 'bg-primary' : ''
                        }`}
                    onPress={() => handleFilterChange(filter as FilterType)}
                    activeOpacity={0.8}>
                    <Text
                        className={`text-text16 tracking-wider ${selectedFilter === filter
                            ? 'text-white'
                            : 'text-secondary'
                            }`}
                        style={fonts.fontMedium}>
                        {filter.charAt(0).toUpperCase() + filter.slice(1)}
                    </Text>
                </TouchableOpacity>
            ))}
        </View>
    );
};

export default FilterTab;
