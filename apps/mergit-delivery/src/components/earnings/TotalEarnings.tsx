import { View, Text, Pressable } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { rupeeSymbol } from 'utils/constants';
import { useTranslation } from 'react-i18next';

const TotalEarnings = () => {
    const { t } = useTranslation();
    return (
        <View className="gap-5 rounded-xl border border-border bg-white px-4 py-5">
            <View className="flex-row items-center gap-4">
                <View className="flex-1 items-center gap-1">
                    <Text
                        style={fonts.fontSemiBold}
                        className="flex-1 text-text16 tracking-wider text-secondary">
                        2
                    </Text>
                    <Text
                        style={fonts.fontRegular}
                        className="flex-1 text-text14 tracking-wider text-secondary">
                        {t('shifts')}
                    </Text>
                </View>
                <View className="h-full w-px bg-border" />
                <View className="flex-1 items-center gap-1">
                    <Text
                        style={fonts.fontSemiBold}
                        className="flex-1 text-text16 tracking-wider text-secondary">
                        1:30 hrs
                    </Text>
                    <Text
                        style={fonts.fontRegular}
                        className="flex-1 text-text14 tracking-wider text-secondary">
                        {t('login_hours')}
                    </Text>
                </View>
                <View className="h-full w-px bg-border" />
                <View className="flex-1 items-center gap-1">
                    <Text
                        style={fonts.fontSemiBold}
                        className="flex-1 text-text16 tracking-wider text-secondary">
                        3
                    </Text>
                    <Text
                        style={fonts.fontRegular}
                        className="flex-1 text-text14 tracking-wider text-secondary">
                        {t('orders')}
                    </Text>
                </View>
            </View>
            <View className="h-px w-full bg-border" />
            <Pressable className="flex-row items-center gap-2">
                <Text
                    style={fonts.fontRegular}
                    className="flex-1 text-text16 tracking-wider text-secondary">
                    {t('totalEarnings')}
                </Text>

                <Text style={fonts.fontBold} className="text-text15 tracking-wide text-primary">
                    {rupeeSymbol}90
                </Text>
            </Pressable>
        </View>
    );
};

export default TotalEarnings;
