import { View, Text, Pressable } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { Entypo } from '@expo/vector-icons';
import { rupeeSymbol } from 'utils/constants';
import { router } from 'expo-router';

const orderData = [
    {
        id: '#12547645',
        shopName: 'SS Hyderabad Biriyani',
        status: 'Delivered',
        date: 'Feb 21, 9:45 PM',
        amount: 30,
    },
];

const SingleOrder = () => {
    const totalOrders = orderData.length;
    const totalAmount = orderData.reduce((acc, order) => acc + order.amount, 0);

    return (
        <View className="rounded-xl bg-white p-primary">
            <Text style={fonts.fontSemiBold} className="text-text16 tracking-wide text-secondary">
                {totalOrders} Orders · Total : {rupeeSymbol}
                {totalAmount}
            </Text>
            <View className="mt-3 border-b border-border" />

            {orderData.map((order, index) => (
                <Pressable
                    key={index}
                    className="mt-4 rounded-lg border border-border p-3"
                    onPress={() =>
                        router.push({
                            pathname: '/specific-order',
                            params: {
                                order: JSON.stringify(order),
                            },
                        })
                    }
                >
                    <View className="flex-row items-center justify-between">
                        <Text
                            style={fonts.fontSemiBold}
                            className="flex-1 text-text16 tracking-wide text-secondary"
                            numberOfLines={1}
                            ellipsizeMode="tail">
                            {order.shopName}
                        </Text>
                        <View className="flex-row items-center gap-1">
                            <View className="rounded-full border border-[#17B26A] bg-[#DCFAE6] px-2 py-1">
                                <Text
                                    className="text-text13 text-[#067647]"
                                    style={fonts.fontMedium}>
                                    {order.status}
                                </Text>
                            </View>
                            <Entypo name="chevron-right" size={22} color="#1F7575" />
                        </View>
                    </View>
                    <Text style={fonts.fontMedium} className="my-3 text-text15 text-secondary tracking-wide">
                        Order ID&nbsp;
                        <Text style={fonts.fontSemiBold} className="text-text15 text-secondary">
                            {order.id}
                        </Text>
                    </Text>
                    <Text style={fonts.fontMedium} className="mb-1 text-text15 text-secondary tracking-wide">
                        {order.date} · Cash Collected&nbsp;
                        <Text className="text-text15 tracking-wide text-secondary" style={fonts.fontSemiBold}>
                            : {rupeeSymbol}
                            {order.amount}
                        </Text>
                    </Text>
                </Pressable>
            ))}
        </View>
    );
};

export default SingleOrder;
