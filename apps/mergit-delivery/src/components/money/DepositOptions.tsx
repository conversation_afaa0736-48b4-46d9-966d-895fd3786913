import { Pressable, View } from 'react-native'
import { CustomText } from '../common'
import { fonts } from '@mergit-mobile/styles'
import { colors } from '~/utils/constants'
import { Ionicons } from '@expo/vector-icons'
import { useBottomSheet } from '@mergit-mobile/components'
import { Image } from 'expo-image'
import { useTabsStore } from '~/services/tabs'

const DepositOptions = () => {
    const { hideBottomSheet } = useBottomSheet();
    const { bottomInset } = useTabsStore();

    return (
        <View className="gap-4 px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
            
            <View className="flex-row items-center justify-between">
                <CustomText
                    tKey="deposit_options"
                    className="text-text16 tracking-wide text-secondary"
                    style={fonts.fontMedium}
                    testID="deposit_options"
                />

                <Pressable
                    testID="close_deposit_options"
                    onPress={hideBottomSheet}
                    className="h-10 w-10 items-center justify-center rounded-lg border border-primary">
                    <Ionicons name="close" size={24} color={colors.secondary} />
                </Pressable>
            </View>

            <View className="w-full flex-row items-center gap-4">
                <Pressable
                    testID="gpay_deposit"
                    className="rounded-lg border border-border px-2 py-[5px]">
                    <Image
                        source={require('assets/images/payments/GPay.png')}
                        style={{ height: 35, width: 35 }}
                        contentFit="cover"
                    />
                </Pressable>
                <Pressable testID="phonepe_deposit">
                    <Image
                        source={require('assets/images/payments/PhonePe.png')}
                        style={{
                            height: 45,
                            width: 45,
                            borderRadius: 8,
                        }}
                        contentFit="cover"
                    />
                </Pressable>
            </View>
        </View>
    )
}

export default DepositOptions