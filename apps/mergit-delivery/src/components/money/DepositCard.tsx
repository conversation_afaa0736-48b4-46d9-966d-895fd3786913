import { View, Text } from 'react-native';
import { fonts, shadow } from '@mergit-mobile/styles';
import { colors, rupeeSymbol } from 'utils/constants';
import { CustomBtn, useBottomSheet } from '@mergit-mobile/components';
import { useTranslation } from 'react-i18next';
import DepositOptions from './DepositOptions';

export const DepositCard = () => {
    const { t } = useTranslation();
    const { showBottomSheet } = useBottomSheet();

    const handleDepositOptions = () => {
        showBottomSheet(() => <DepositOptions />);
    };

    return (
        <View className="gap-5">
            <Text className="text-text16 tracking-wide text-secondary" style={fonts.fontSemiBold}>
                {t('wallet')} ({t('date')} Feb 1 - Feb 7)
            </Text>

            <View className="gap-5 rounded-lg bg-white p-primary" style={shadow}>
                <View className="flex-row items-center gap-3">
                    <Text
                        className="flex-1 text-text15 tracking-wide text-secondary"
                        style={fonts.fontMedium}>
                        {t('walletBalance')}
                    </Text>

                    <Text
                        className="text-text15 tracking-wider text-secondary"
                        style={fonts.fontSemiBold}>
                        {rupeeSymbol}2000
                    </Text>
                </View>

                <View className="flex-row items-center gap-3">
                    <Text
                        className="flex-1 text-text15 tracking-wide text-secondary"
                        style={fonts.fontMedium}>
                        {t('cash_in_hand')}
                    </Text>

                    <Text
                        className="text-text15 tracking-wider text-secondary"
                        style={fonts.fontSemiBold}>
                        -{rupeeSymbol}1000
                    </Text>
                </View>

                <View className="h-px w-full bg-border" />

                <View className="flex-row items-center gap-3">
                    <Text
                        className="flex-1 text-text15 tracking-wide text-secondary"
                        style={fonts.fontMedium}>
                        {t('total_earnings')}
                    </Text>

                    <Text
                        className="text-text15 tracking-wider text-secondary"
                        style={fonts.fontSemiBold}>
                        {rupeeSymbol}1000
                    </Text>
                </View>

                <Text
                    className="text-text15 tracking-wide text-secondary"
                    style={fonts.fontRegular}>
                    {t('cash_in_hand_limit', { amount: `${rupeeSymbol}2000` })}
                </Text>

                <CustomBtn
                    title={t('deposit')}
                    onPress={handleDepositOptions}
                    height={50}
                    borderColor={colors.primary}
                    backgroundColor="#fff"
                    textStyle={{ color: colors.primary }}
                />
            </View>
        </View>
    );
};
