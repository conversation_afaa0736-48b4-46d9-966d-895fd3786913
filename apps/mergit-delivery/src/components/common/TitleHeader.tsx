import React, { useContext, useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { fonts } from '@mergit-mobile/styles';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Siren } from 'assets/icons/header';
import { StepProgress } from './StepProgress';
import HandS from './HandS';
import { CustomBottomSheet, CustomSwitch } from '@mergit-mobile/components';
import { useLocationStore } from '~/services/location';
import { useOrderStore } from 'services/orders';
import { SocketContext } from 'context/SocketContext';

interface TitleHeaderProps {
    title?: string;
    backArrowColor?: string;
    onPressBack?: () => void;
    onPressLeft?: () => void;
    backArrow?: boolean | 'arrow-left' | 'chevron-left';
    iconLeftName?: string;
    iconRight?: React.ReactNode;
    titleLeft?: string;
    subTitleLeft?: string;
    backgroundColor?: string;
    borderBottom?: boolean;
    shadowBottom?: boolean;
    zIndex?: number;
    showCouponTitle?: boolean;
    enhIcon?: boolean;
    nhIcon?: boolean;
    showSwitch?: boolean;
    onPressNotification?: () => void;
    onPressHelpandsupport?: () => void;
    stepPregress?: {
        currentStep: number[];
        totalNumberOfSteps: number[];
        height?: number;
        splitInto?: number;
    };
}

export const TitleHeader = React.memo(
    ({
        title,
        backArrowColor,
        onPressBack = () => {
            router.back();
        },
        onPressLeft,
        backArrow,
        iconLeftName,
        iconRight,
        titleLeft,
        subTitleLeft,
        backgroundColor = '#ffffff',
        borderBottom,
        shadowBottom,
        zIndex = 10,
        enhIcon,
        nhIcon,
        showSwitch,
        onPressHelpandsupport,
        onPressNotification = () => router.push('/notification'),
        stepPregress,
    }: TitleHeaderProps) => {
        const value = useSafeAreaInsets().top;
        const [isModalVisible, setIsModalVisible] = useState(false);

        const { isEnabled, setIsEnabled } = useLocationStore();
        const { updateOrders } = useOrderStore();

        const { socket } = useContext(SocketContext);

        const handleToggle = async (state: boolean) => {
            if (!state) {
                if (socket && socket.connected) {
                    socket.disconnect();
                }
                updateOrders(null);
                setIsEnabled(false);
            } else {
                // api
            }
        };

        return (
            <>
                <View
                    className={`flex-row items-center justify-between px-primary pb-3 pt-0 ${
                        borderBottom ? 'border-b-[1px] border-border' : ''
                    }`}
                    style={[
                        shadowBottom && {
                            backgroundColor: backgroundColor,
                            zIndex,
                            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
                        },
                        {
                            paddingTop: value <= 32 ? value + 10 : value === 0 ? 20 : value,
                            backgroundColor: backgroundColor ?? 'white',
                        },
                    ]}>
                    {/* Left Section (Back Arrow + Title or Location) */}
                    <View className="flex-1 flex-row items-center ">
                        {backArrow && (
                            <TouchableOpacity
                                activeOpacity={0.8}
                                onPress={onPressBack}>
                                <Feather
                                    name={typeof backArrow === 'string' ? backArrow : 'arrow-left'}
                                    size={backArrow === 'chevron-left' ? 25 : 22}
                                    color={backArrowColor ?? '#000'}
                                    testID="back"
                                    style={{
                                        paddingVertical: backArrow === 'chevron-left' ? 0 : 10,
                                    }}
                                />
                            </TouchableOpacity>
                        )}
                        {iconLeftName && (
                            <TouchableOpacity activeOpacity={0.8} onPress={onPressLeft}>
                                <Feather
                                    name={iconLeftName as keyof typeof Feather.glyphMap}
                                    size={19}
                                />
                            </TouchableOpacity>
                        )}

                        {titleLeft && (
                            <View className="flex-1 px-3">
                                <Text
                                    style={fonts.fontSemiBold}
                                    className="text-text16 tracking-wide text-secondary"
                                    numberOfLines={1}
                                    ellipsizeMode="tail">
                                    {titleLeft}
                                </Text>
                                {subTitleLeft && (
                                    <Text
                                        style={fonts.fontMedium}
                                        className="text-text14 tracking-wide text-neutral">
                                        {subTitleLeft}
                                    </Text>
                                )}
                            </View>
                        )}

                        {showSwitch && (
                            <CustomSwitch
                                activeColor="#DCFAE6"
                                inactiveColor="white"
                                toggleColor={isEnabled ? '#17B26A' : '#ffffff'}
                                onLabel="Online"
                                offLabel="Offline"
                                switchStyle={{ width: 95, height: 28 }}
                                toggleStyle={{
                                    width: 35,
                                    height: 35,
                                    borderRadius: 50,
                                    borderWidth: 1,
                                    borderColor: isEnabled ? 'transparent' : '#D5D7DA',
                                }}
                                state={isEnabled}
                                onChange={handleToggle}
                            />
                        )}
                    </View>

                    {/* Center Title */}
                    {(title || stepPregress) && (
                        <View className="native:bottom-[53%] pointer-events-none absolute left-0 right-0 items-center">
                            <Text
                                className="text-text16 tracking-wide text-secondary"
                                style={fonts.fontSemiBold}>
                                {title}
                            </Text>
                            {stepPregress && (
                                <View testID="step-progress" className=" bottom-[30%] w-[40%]">
                                    <StepProgress
                                        currentStep={stepPregress.currentStep || [0]}
                                        totalNumberOfSteps={stepPregress.totalNumberOfSteps || [0]}
                                        height={stepPregress.height || 10}
                                        splitInto={stepPregress.splitInto || 4}
                                    />
                                </View>
                            )}
                        </View>
                    )}

                    <View>
                        {iconRight && <View className="absolute right-0">{iconRight}</View>}
                        {(enhIcon || nhIcon) && (
                            <View className="flex-row items-center gap-1">
                                {enhIcon && (
                                    <TouchableOpacity
                                        testID="emergency-options"
                                        onPress={() =>
                                            router.push('/(protected)/(emergency-options)')
                                        }
                                        activeOpacity={0.8}
                                        className="bottom-[3px] p-2">
                                        <Siren />
                                    </TouchableOpacity>
                                )}
                                <TouchableOpacity
                                    className="p-2"
                                    testID="notification"
                                    onPress={onPressNotification}
                                    activeOpacity={0.8}>
                                    <Feather name="bell" size={25} color="#0A0D12" />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    className="pb-2 pl-2 pt-2"
                                    testID="help-and-support"
                                    onPress={
                                        onPressHelpandsupport
                                            ? onPressHelpandsupport
                                            : () => setIsModalVisible(true)
                                    }
                                    activeOpacity={0.8}>
                                    <Feather name="headphones" size={25} color="#0A0D12" />
                                </TouchableOpacity>
                            </View>
                        )}
                    </View>
                </View>

                <CustomBottomSheet
                    isVisible={isModalVisible}
                    onClose={() => setIsModalVisible(false)}>
                    <HandS isModalVisible={isModalVisible} setIsModalVisible={setIsModalVisible} />
                </CustomBottomSheet>
            </>
        );
    }
);
