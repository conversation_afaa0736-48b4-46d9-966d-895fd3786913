import { View, Text, TouchableOpacity } from 'react-native';
import { Entypo } from '@expo/vector-icons';
import { fonts } from '@mergit-mobile/styles';
import { router } from 'expo-router';
import { colors } from '~/utils/constants';

const emergencyList = [
    { id: 1, title: 'Emergency Call' },
    { id: 2, title: 'Emergency Contact Details' },
];

interface EmergencyListCardProps {
    openBottomSheet: () => void;
}

const EmergencyListCard = ({ openBottomSheet }: EmergencyListCardProps) => {
    return (
        <View className="flex-1">
            <View className="flex-1 gap-4">
                {emergencyList.map((item) => (
                    <TouchableOpacity
                        key={item.id}
                        onPress={() => {
                            if (item.title === 'Emergency Call') {
                                openBottomSheet();
                            } else if (item.title === 'Emergency Contact Details') {
                                router.push('/contact-list');
                            }
                        }}
                        activeOpacity={0.9}>
                        <View className="flex-row items-center gap-4 rounded-lg border border-border bg-white p-4">
                            <Text className="flex-1 text-text16 text-secondary tracking-wider" style={fonts.fontMedium}>
                                {item.title}
                            </Text>
                            <Entypo name="chevron-small-right" size={24} color={colors.neutral} />
                        </View>
                    </TouchableOpacity>
                ))}
            </View>
        </View>
    );
};

export default EmergencyListCard;
