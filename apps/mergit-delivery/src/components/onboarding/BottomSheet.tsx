import React, { useEffect } from 'react';
import { Dimensions, Pressable, StyleSheet } from 'react-native';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    Easing,
    runOnJS,
} from 'react-native-reanimated';

const { height } = Dimensions.get('window');

interface BottomSheetProps {
    isVisible: boolean;
    onClose: () => void;
    children: React.ReactNode;
}

export const BottomSheet: React.FC<BottomSheetProps> = ({ isVisible, onClose, children }) => {
    const translateY = useSharedValue(height);
    const backdropOpacity = useSharedValue(0);

    useEffect(() => {
        if (isVisible) {
            translateY.value = withTiming(0, {
                duration: 300,
                easing: Easing.out(Easing.ease),
            });
            backdropOpacity.value = withTiming(1, { duration: 300 });
        } else {
            translateY.value = withTiming(height, {
                duration: 300,
                easing: Easing.in(Easing.ease),
            });
            backdropOpacity.value = withTiming(0, { duration: 200 }, (finished) => {
                if (finished) runOnJS(onClose)();
            });
        }
    }, [isVisible]);

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateY: translateY.value }],
    }));

    const backdropStyle = useAnimatedStyle(() => ({
        opacity: backdropOpacity.value,
    }));

    return (
        <>
            {isVisible && (
                <Animated.View
                    style={[
                        {
                            ...StyleSheet.absoluteFillObject,
                            backgroundColor: 'rgba(0,0,0,0.5)',
                            zIndex: 1,
                        },
                        backdropStyle,
                    ]}>
                    <Pressable
                        style={{ flex: 1 }}
                        onPress={() => {
                            translateY.value = withTiming(height, {
                                duration: 300,
                            });
                            backdropOpacity.value = withTiming(0, { duration: 200 }, () =>
                                runOnJS(onClose)()
                            );
                        }}
                    />
                </Animated.View>
            )}

            <Animated.View
                style={[
                    {
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        backgroundColor: 'white',
                        borderTopLeftRadius: 16,
                        borderTopRightRadius: 16,
                        zIndex: 2,
                    },
                    animatedStyle,
                ]}>
                {children}
            </Animated.View>
        </>
    );
};
