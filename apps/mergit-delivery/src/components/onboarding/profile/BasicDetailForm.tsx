import { View, Text } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { InputField, CustomBtn, RadioButton } from '@mergit-mobile/components';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

const BasicDetailForm = ({
    control,
    setValue,
    errors,
    formData,
    updateField,
    gender,
    setGender,
    maritalStatus,
    setMaritalStatus,
    physicallyChallanged,
    setPhysicallyChallanged,
    onPress,
}: any) => {
    const { t } = useTranslation();

    const options = [
        {
            id: 1,
            label: t('onboarding.basicDetails.genderTitle.options.1'),
            value: 'male',
        },
        {
            id: 2,
            label: t('onboarding.basicDetails.genderTitle.options.2'),
            value: 'female',
        },
        {
            id: 3,
            label: t('onboarding.basicDetails.genderTitle.options.3'),
            value: 'others',
        },
    ];

    const maritalStatusOptions = [
        {
            id: 1,
            label: t('onboarding.basicDetails.maritalStatusTitle.options.1'),
            value: 'single',
        },
        {
            id: 2,
            label: t('onboarding.basicDetails.maritalStatusTitle.options.2'),
            value: 'married',
        },
    ];

    const physicallyChallangedOptions = [
        {
            id: 1,
            label: t('onboarding.basicDetails.physicallyChallangedTitle.options.1'),
            value: 'yes',
        },
        {
            id: 2,
            label: t('onboarding.basicDetails.physicallyChallangedTitle.options.2'),
            value: 'no',
        },
    ];

    return (
        <View className="flex-1 gap-8">
            <Text style={fonts.fontSemiBold} className="text-text18 tracking-wider text-secondary">
                {t('onboarding.basicDetails.title')}
            </Text>

            <Controller
                control={control}
                name="name"
                render={({ field: { onChange, onBlur, value } }) => (
                    <InputField
                        label={t('onboarding.basicDetails.fullName')}
                        testID="Name"
                        placeholder={t('onboarding.basicDetails.enterFullName')}
                        value={value}
                        keyboardType="default"
                        inputMode="text"
                        onChangeText={(text) => {
                            onChange(text);
                            updateField('name', text);
                        }}
                        onBlur={onBlur}
                        errorMsg={errors?.name?.message?.toString()}
                        autoCapitalize='words'
                    />
                )}
            />

            <Controller
                control={control}
                name="email"
                render={({ field: { onChange, onBlur, value } }) => (
                    <InputField
                        label={t('onboarding.basicDetails.email')}
                        testID="Email"
                        placeholder={t('onboarding.basicDetails.enterEmail')}
                        keyboardType="email-address"
                        inputMode="email"
                        value={value}
                        onChangeText={(text) => {
                            onChange(text);
                            updateField('email', text);
                        }}
                        onBlur={onBlur}
                        errorMsg={errors?.email?.message?.toString()}
                    />
                )}
            />

            <View>
                <View className="gap-4">
                    <Text
                        style={fonts.fontSemiBold}
                        className="text-text16 tracking-wide text-secondary">
                        {t('onboarding.basicDetails.genderTitle.title')}
                    </Text>
                    <RadioButton
                        label=""
                        testID="gender"
                        options={options}
                        value={gender || formData['gender']}
                        onChange={(value) => {
                            setGender(value);
                            updateField('gender', value);
                            setValue('gender', value, {
                                shouldValidate: true,
                            });
                        }}
                    />
                </View>
                {errors?.gender && (
                    <Text
                        style={fonts.fontRegular}
                        className="mt-1 text-text14 tracking-wide text-errorPrimary">
                        {String(errors?.gender?.message)}
                    </Text>
                )}
            </View>

            <View>
                <View className="gap-4">
                    <Text style={fonts.fontSemiBold} className=" text-text17 text-secondary">
                        {t('onboarding.basicDetails.maritalStatusTitle.title')}
                    </Text>
                    <RadioButton
                        label=""
                        testID="maritalStatus"
                        options={maritalStatusOptions}
                        value={maritalStatus || formData['maritalStatus']}
                        onChange={(value) => {
                            setMaritalStatus(value);
                            updateField('maritalStatus', value);
                            setValue('maritalStatus', value, {
                                shouldValidate: true,
                            });
                        }}
                    />
                </View>
                {errors?.maritalStatus && (
                    <Text
                        style={fonts.fontRegular}
                        className="mt-1 text-text14 tracking-wide text-errorPrimary">
                        {String(errors?.maritalStatus.message)}
                    </Text>
                )}
            </View>

            <View>
                <View className="gap-4">
                    <Text style={fonts.fontSemiBold} className=" text-text17 text-secondary">
                        {t('onboarding.basicDetails.physicallyChallangedTitle.title')}
                    </Text>
                    <RadioButton
                        label=""
                        testID="physicallyChallanged"
                        options={physicallyChallangedOptions}
                        value={physicallyChallanged || formData['physicallyChallanged']}
                        onChange={(value) => {
                            setPhysicallyChallanged(value);
                            updateField('physicallyChallanged', value);
                            setValue('physicallyChallanged', value, {
                                shouldValidate: true,
                            });
                        }}
                    />
                </View>
                {errors?.physicallyChallanged && (
                    <Text
                        style={fonts.fontRegular}
                        className="mt-1 text-text14 tracking-wide text-errorPrimary">
                        {String(errors?.physicallyChallanged?.message)}
                    </Text>
                )}
            </View>

            <View className="flex-1" />

            <CustomBtn title={t('confirm')} onPress={onPress} />
        </View>
    );
};

export default BasicDetailForm;
