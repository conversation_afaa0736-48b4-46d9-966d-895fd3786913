import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { useCallback, useRef, useState, useEffect, useMemo } from 'react';
import { fonts } from '@mergit-mobile/styles';
import { Ionicons } from '@expo/vector-icons';
import { OtpInput, OtpInputRef } from 'react-native-otp-entry';
import { CustomBtn } from '@mergit-mobile/components';
import { CustomText } from 'components/common';
import { useTranslation } from 'react-i18next';
import { BottomSheet } from '../BottomSheet';
import { useTabsStore } from '~/services/tabs';

const VerifyEmailMode = ({ isOpen, onClose, email, setIsBasicFormCompleted }: any) => {
    const [otp, setOtp] = useState('');
    const otpInputRef = useRef<OtpInputRef>(null);
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const [countdown, setCountdown] = useState(30);

    useEffect(() => {
        if (!countdown) return;

        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev === 1) {
                    clearInterval(timer);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [countdown]);

    const handleCompleteSheet = useCallback(() => {
        onClose();
        Keyboard.dismiss();
        setIsBasicFormCompleted(true);
        setOtp('');
        otpInputRef.current?.clear();
    }, [onClose]);

    const handleCloseSheet = useCallback(() => {
        onClose();
        Keyboard.dismiss();
    }, [onClose]);

    const formatCoutdown = useMemo(() => {
        return `${countdown < 10 ? `00:0${countdown}` : `00:${countdown}`}`;
    }, [countdown]);

    return (
        <BottomSheet isVisible={isOpen} onClose={onClose}>
            <View className="flex gap-8 px-primary pt-primary" style={{ paddingBottom: bottomInset }}>

                <View className='gap-5'>
                    <View className="flex-row items-center justify-between ">
                    <CustomText
                        tKey="otpVerify.emailVerify"
                        style={fonts.fontSemiBold}
                        className="text-text17 text-secondary"
                    />
                    <TouchableOpacity
                        activeOpacity={0.8}
                        testID="close"
                        onPress={handleCloseSheet}
                            className="h-10 w-10 items-center justify-center rounded-lg border border-primary">
                            <Ionicons name="close" size={24} color="#1F7575" />
                    </TouchableOpacity>
                </View>

                    <Text style={fonts.fontMedium} className="text-text15 text-neutral tracking-wide">
                    {t('otpVerify.6digit')}
                        <Text style={fonts.fontSemiBold} className="text-text15 text-secondary tracking-wider">
                        {email}
                    </Text>
                </Text>
                </View>

                <View className="items-center justify-center gap-4 self-center mid-lg:w-1/2">
                    <OtpInput
                        ref={otpInputRef}
                        numberOfDigits={6}
                        onFilled={(text) => setOtp(text)}
                        autoFocus={false}
                        focusColor="#1F7575"
                        type="numeric"
                        theme={{
                            pinCodeContainerStyle: {
                                borderWidth: 1,
                                borderRadius: 8,
                                width: 50,
                                height: 50,
                            },
                            pinCodeTextStyle: {
                                fontSize: 17,
                                color: '#0A0D12',
                                fontFamily: 'DMSans-Regular',
                            },
                        }}
                    />

                    <View className="flex-row items-center gap-1">
                        <CustomText
                            tKey="otpVerify.noOtp"
                            style={fonts.fontMedium}
                            className="tracking-wide text-text15 text-secondary"
                        />
                        <Text
                            style={fonts.fontSemiBold}
                            className={`text-text15 text-secondary ${
                                countdown === 0 ? 'underline' : ''
                            }`}>
                            {countdown === 0 ? 'Resend OTP' : `Retry in ${formatCoutdown}`}
                        </Text>

                    </View>
                </View>

                <CustomBtn
                    title={t('otpVerify.verify')}
                    height={50}
                    disabled={otp.length !== 6}
                    onPress={handleCompleteSheet}
                />
            </View>
        </BottomSheet>
    );
};

export default VerifyEmailMode;
