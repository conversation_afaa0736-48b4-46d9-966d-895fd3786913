import { Feather } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, TextInput } from 'react-native';
import { CustomBtn } from '@mergit-mobile/components';
import { useCityStore } from 'services/city';
import { TitleHeader } from 'components/common';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';
import { fonts } from '@mergit-mobile/styles';
import { colors } from '~/utils/constants';

const SelectCityScreen = () => {
    const { cities, selectedCity, setSelectedCity } = useCityStore();
    const [localSelect, setLocalSelect] = useState(selectedCity);
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const [search, setSearch] = useState('');

    const filteredCities = cities.filter((c) =>
        c.name.toLowerCase().includes(search.toLowerCase())
    );

    const handleCitySelect = (city: any) => {
        setSelectedCity(city);
        router.back();
    };

    return (
        <View className="flex-1 bg-white">

            <TitleHeader backArrow="chevron-left" borderBottom title={t('city.selectCity')} />

            <View className='flex-1 px-primary mt-5' style={{ paddingBottom: bottomInset }}>

                <View
                    testID="search-container"
                    className="flex-row items-center gap-4 rounded-lg border border-border px-4">
                    <Feather testID="search-icon" name="search" size={20} color="#717680" />
                    <TextInput
                        testID="search-input"
                        placeholder={t('city.searchCity')}
                        value={search}
                        cursorColor={'#1F7575'}
                        onChangeText={setSearch}
                        placeholderTextColor="#999"
                        className="w-full py-4 text-text16 text-secondary tracking-wide"
                    />
                </View>

                <FlatList
                    data={filteredCities}
                    keyExtractor={(item) => item.id}
                    showsVerticalScrollIndicator={false}
                    testID="city-list"
                    renderItem={({ item }) => (
                        <TouchableOpacity
                            testID={`city-item-${item.name}`}
                            className={`flex-row items-center gap-4 py-6 ${item.id !== filteredCities[filteredCities.length - 1].id
                                ? 'border-b border-border'
                                : ''
                                }`}
                            onPress={() => setLocalSelect(item)}
                            activeOpacity={0.9}>
                            <Text style={fonts.fontMedium} className="flex-1 text-text16 text-secondary tracking-wide">{item.name}</Text>
                            <View
                                className={`h-6 w-6 items-center justify-center rounded-full border-2 ${localSelect?.id === item?.id
                                    ? 'border-primary'
                                    : 'border-neutral'
                                    }`}>
                                {localSelect?.id === item?.id && (
                                    <View
                                        style={{
                                            height: 11,
                                            width: 11,
                                            borderRadius: 6,
                                            backgroundColor: colors.primary,
                                        }}
                                    />
                                )}
                            </View>
                        </TouchableOpacity>
                    )}
                    contentContainerClassName='flex-grow'
                />

                <CustomBtn
                    onPress={() => handleCitySelect(localSelect)}
                    title={t('continue')}
                    disabled={!selectedCity}
                />

            </View>

        </View>
    );
};

export default SelectCityScreen;
