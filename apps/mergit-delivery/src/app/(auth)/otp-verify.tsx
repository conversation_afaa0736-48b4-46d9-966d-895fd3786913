import { View, Text, Pressable } from 'react-native';
import { useEffect, useState } from 'react';
import { OtpInput } from 'react-native-otp-entry';
import { useLocalSearchParams } from 'expo-router';
import { CustomBtn } from '@mergit-mobile/components';
import { fonts } from '@mergit-mobile/styles';
import { CustomText, TitleHeader } from 'components/common';
import { useTranslation } from 'react-i18next';
import { colors } from 'utils/constants';
import { useAuthApi } from '~/services/auth/authApis';
import { useAuthStore } from '~/services/auth';
import { Toast, useGlobalContext } from '~/context';
import { useCityStore } from '~/services/city';
import { useTabsStore } from '~/services/tabs';

const OtpVerify = () => {
    const { mobileNumber } = useLocalSearchParams();
    const [otp, setOtp] = useState('');

    const [countdown, setCountdown] = useState(30);
    const { setAuthToken, setRefreshToken, login } = useAuthStore();

    const { selectedCity } = useCityStore();
    const { setLoading } = useGlobalContext();
    const { bottomInset } = useTabsStore();

    const { t } = useTranslation();

    useEffect(() => {
        if (countdown === 0) return;

        const timer = setInterval(() => {
            setCountdown((prev) => prev - 1);
        }, 1000);

        return () => clearInterval(timer);
    }, [countdown]);

    const maskedMobile = (number: string) => {
        if (number?.length === 10) {
            return 'XXXXXX' + number.slice(6);
        }
        return number;
    };

    const { registerPartner, verifyOtp } = useAuthApi();

    const resendOtp = async () => {
        if (mobileNumber) {
            setCountdown(30);
            try {
                const res = await registerPartner.mutateAsync(mobileNumber as string);
                console.log(res);
                if (res.data.status) {
                    Toast.show({
                        message: 'OTP sent again successfully',
                    });
                }
            } catch (err) {
                console.log('Error in resending otp : ', err);
            }
        }
    };

    const handleOtpSubmit = async () => {
        setLoading(true);
        try {
            const response = await verifyOtp.mutateAsync({
                identifier: mobileNumber as string,
                otp,
            });
            console.log(response);
            if (selectedCity) {
                login(response.data.accessToken);
                setRefreshToken(response.data.refreshToken);
                setAuthToken(response.data.accessToken);
            } else {
                setAuthToken(response.data.accessToken);
            }
        } catch (error) {
            console.log('OTP verification failed : ', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow="chevron-left" />

            <View className="flex-1 px-primary" style={{ paddingBottom: bottomInset }}>
                <View className="flex-1 gap-8">
                    <View className="gap-2">
                        <CustomText
                            tKey="otpVerify.title"
                            style={fonts.fontBold}
                            className="text-text18 tracking-wide text-secondary"
                        />
                        <Text
                            style={fonts.fontMedium}
                            className="text-text15 tracking-wide text-neutral">
                            {t('otpVerify.subtitle')}&nbsp;
                            <Text
                                className="text-text15 tracking-wide text-neutral"
                                style={fonts.fontMedium}>
                                {maskedMobile(mobileNumber as string)}
                            </Text>
                        </Text>
                    </View>

                    <View className="w-full self-center lg:w-1/2">
                        <OtpInput
                            numberOfDigits={6}
                            focusColor={colors.primary}
                            autoFocus
                            type="numeric"
                            onFilled={(text) => setOtp(text)}
                            onTextChange={(text) => setOtp(text)}
                            theme={{
                                containerStyle: {
                                    width: '100%',
                                },
                                pinCodeContainerStyle: {
                                    borderWidth: 1,
                                    borderRadius: 8,
                                    width: 50,
                                    height: 50,
                                    borderColor: '#ddd',
                                },
                                pinCodeTextStyle: {
                                    fontSize: 17,
                                    color: colors.secondary,
                                    ...fonts.fontMedium,
                                },
                            }}
                        />
                    </View>

                    <View className="items-center gap-1">
                        <Text
                            style={fonts.fontMedium}
                            className="text-center text-text15 tracking-wide text-neutral">
                            {t('otpVerify.noOtp')}{' '}
                            {countdown !== 0 &&
                                `00 : ${countdown < 10 ? `0${countdown}` : countdown}`}
                        </Text>

                        {countdown === 0 && (
                            <Pressable hitSlop={10} onPress={resendOtp}>
                                <CustomText
                                    tKey="otpVerify.resend"
                                    style={fonts.fontMedium}
                                    className="text-text15 text-primary underline"
                                />
                            </Pressable>
                        )}
                    </View>
                </View>

                <CustomBtn
                    title={t('continue')}
                    disabled={otp.length !== 6}
                    height={50}
                    testID="continue-btn"
                    onPress={handleOtpSubmit}
                />
            </View>

        </View>
    );
};

export default OtpVerify;
