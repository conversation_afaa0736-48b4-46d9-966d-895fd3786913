import { View, Text } from 'react-native';
import { useEffect } from 'react';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import { fonts } from '@mergit-mobile/styles';
import { CustomBtn } from '@mergit-mobile/components';
import { pin } from 'assets/images/maps';
import { useCityStore, fetchCities } from 'services/city';
import { CustomText, TitleHeader } from 'components/common';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '~/services/auth';
import { useTabsStore } from '~/services/tabs';

const City = () => {
    const { selectedCity, setSelectedCity, setCities } = useCityStore();
    const { login, authToken } = useAuthStore();
    const { bottomInset } = useTabsStore();

    const { t } = useTranslation();

    useEffect(() => {
        const loadCities = async () => {
            const cities = await fetchCities('India', 'Tamil Nadu');
            setCities(cities);
            if (!selectedCity) {
                const defaultCity = cities.find((c: any) => c.name.toLowerCase() === 'chennai');
                if (defaultCity) {
                    setSelectedCity(defaultCity);
                }
            }
        };
        loadCities();
    }, []);

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow="chevron-left" />
            <View className="flex-1 gap-6">
                <View
                    testID="marker-container"
                    className="h-40 w-40 items-center justify-center self-center overflow-hidden rounded-full bg-primaryBg">
                    <Image
                        source={pin}
                        testID="marker-image"
                        style={{ width: '70%', height: '70%' }}
                        contentFit="contain"
                    />
                </View>

                <CustomText
                    tKey="city.title"
                    className="text-center text-text16 text-secondary tracking-wider"
                    style={fonts.fontSemiBold}
                />

                <Text className="text-center text-text20 text-secondary" style={fonts.fontSemiBold}>
                    {selectedCity?.name}
                </Text>
            </View>

            <View className='px-primary gap-6' style={{ paddingBottom: bottomInset }}>
                <CustomText
                    tKey="city.subtitle"
                    style={fonts.fontSemiBold}
                    className="text-center text-text16 text-primary underline"
                    onPress={() => router.push('/select-city')}
                />
                <CustomBtn onPress={() => login(authToken!)} title={t('continue')} height={50} />
            </View>
        </View>
    );
};

export default City;
