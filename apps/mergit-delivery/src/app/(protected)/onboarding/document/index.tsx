import React from 'react';
import { View, Text, FlatList, Platform } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { TitleHeader } from 'components/common';
import { Divider, DocCard } from '~/components/onboarding';
import { useDocumentStore } from '~/services/onbording';
import { router } from 'expo-router';
import { CustomBtn } from '@mergit-mobile/components';
import { useTranslation } from 'react-i18next';

const Document = () => {
    const { t } = useTranslation();
    const { documents } = useDocumentStore();

    const pendingDocs = documents.filter((doc) => doc.status === 'pending');
    const completedDocs = documents.filter((doc) => doc.status === 'completed');
    const completedDocsCount = documents.filter((doc) => doc.status === 'completed').length;

    const sections = [
        { title: t('onboarding.doc.Pending'), data: pendingDocs },
        { title: t('onboarding.doc.Completed'), data: completedDocs },
    ];

    return (
        <View className="flex-1 bg-white">
            <TitleHeader
                backArrow="chevron-left"
                stepPregress={{
                    currentStep: [1, 1, completedDocsCount, 0],
                    totalNumberOfSteps: [1, 1, 5, 0],
                    height: 10,
                    splitInto: 4,
                }}
                nhIcon
            />
            <View className="flex-1 gap-4 px-primary">
                <Text style={fonts.fontSemiBold} className="text-text18 text-secondary">
                    {t('onboarding.doc.title')}
                </Text>
                <Text style={fonts.fontMedium} className="text-text15 text-secondary">
                    {t('onboarding.doc.subtitle')}
                </Text>

                {sections.map(
                    ({ title, data }) =>
                        data.length > 0 && (
                            <View className="gap-5" key={title}>
                                <Divider title={title} />
                                <FlatList
                                    data={data}
                                    renderItem={({ item, index }) => (
                                        <DocCard
                                            item={item}
                                            index={index}
                                            handleDocumentPress={() =>
                                                router.push(`/onboarding/document/${item.value}`)
                                            }
                                        />
                                    )}
                                    keyExtractor={(_, index) => `${title.toLowerCase()}-${index}`}
                                    scrollEnabled={false}
                                />
                            </View>
                        )
                )}
                <View className="flex-[0.9] " />

                <CustomBtn
                    title={t('onboarding.doc.submit')}
                    onPress={() =>
                        router.push({
                            pathname: '/onboarding/successscreen',
                            params: {
                                completedScreen: 'Document',
                            },
                        })
                    }
                    disabled={pendingDocs.length > 0}
                    btnStyle={{ bottom: Platform.OS === 'ios' ? 20 : 4 }}
                />
            </View>
        </View>
    );
};

export default Document;
