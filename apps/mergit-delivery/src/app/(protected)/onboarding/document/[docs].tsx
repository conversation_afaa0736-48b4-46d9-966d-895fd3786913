import {
    Text,
    View,
    Pressable,
    Image,
    Dimensions,
    Platform,
    Keyboard,
    ScrollView,
    Linking,
    Alert,
} from 'react-native';
import React, { useRef, useState } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import { TitleHeader } from 'components/common';
import { DocCamera, UploadOptionsSheet } from '~/components/onboarding';
import { fonts } from '@mergit-mobile/styles';
import { InputField, CustomBtn } from '@mergit-mobile/components';
import { Feather } from '@expo/vector-icons';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    Easing,
    runOnJS,
} from 'react-native-reanimated';
import {
    formatDocumentNumber,
    getInputProps,
    getValidationSchema,
    sanitizeDocumentNumber,
    useDocStore,
    useDocumentStore,
} from '~/services/onbording';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import * as ImagePicker from 'expo-image-picker';
import BottomSheet from '@gorhom/bottom-sheet';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useTranslation } from 'react-i18next';

const { width } = Dimensions.get('window');

const Docs = () => {
    const { t } = useTranslation();
    const { docs } = useLocalSearchParams();
    const { documents, updateDocumentStatus } = useDocumentStore();
    const { updateDoc, docs: docUploads, updateDocNumber } = useDocStore();
    const bottomSheetRef = useRef<BottomSheet>(null);

    const completedDocsCount = documents.filter((doc) => doc.status === 'completed').length;

    const getCardName = (docType: string | string[] | undefined) => {
        switch (docType) {
            case 'aadhar':
                return t('onboarding.doc.docName.aadhar');
            case 'pan':
                return t('onboarding.doc.docName.pan');
            case 'license':
                return t('onboarding.doc.docName.license');
            case 'rc':
                return t('onboarding.doc.docName.rc');
            case 'selfie':
                return t('onboarding.doc.docName.selfie');
            default:
                return '';
        }
    };

    const cardName = getCardName(docs);
    const docKey = docs as string;

    const validationSchema = getValidationSchema(docKey, getCardName);

    type FormData = yup.InferType<typeof validationSchema>;

    const {
        control,
        handleSubmit,
        formState: { errors },
        trigger,
    } = useForm<FormData>({
        resolver: yupResolver(validationSchema),
        defaultValues: {
            number: docUploads?.[docKey]?.number
                ? formatDocumentNumber(docUploads[docKey].number as string, docKey)
                : '',
        },
        mode: 'onChange',
    });

    const [cameraConfig, setCameraConfig] = useState({
        type: docKey,
        view: 'front',
        forDoc: docKey,
        cardName: cardName,
    });
    const [cameraVisible, setCameraVisible] = useState(docKey === 'selfie');
    const [sheetVisible, setSheetVisible] = useState(false);
    const [selectedSide, setSelectedSide] = useState<'front' | 'back'>('front');
    const [isReupload, setIsReupload] = useState(false);
    const translateX = useSharedValue(0);

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateX: translateX.value }],
    }));

    const openCamera = (side: 'front' | 'back') => {
        setCameraConfig({
            type: docKey,
            view: side,
            forDoc: docKey,
            cardName: cardName,
        });
        translateX.value = width;
        setCameraVisible(true);
        setSheetVisible(false);
        setIsReupload(false);
        bottomSheetRef.current?.close();
        translateX.value = withTiming(0, {
            duration: 300,
            easing: Easing.out(Easing.ease),
        });
    };

    const closeCamera = () => {
        translateX.value = withTiming(
            width,
            { duration: 300, easing: Easing.out(Easing.ease) },
            (finished) => {
                if (finished) runOnJS(setCameraVisible)(false);
            }
        );
    };

    const handleCapture = (uri: string) => {
        updateDoc(cameraConfig.type as any, cameraConfig.view as any, uri);
        closeCamera();
    };

    const pickImage = async (side: 'front' | 'back') => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
            Alert.alert(
                'Permission Required',
                'Please enable access to your gallery in settings.',
                [
                    {
                        text: 'Go to Settings',
                        onPress: () => {
                            if (Platform.OS !== 'web') {
                                Linking.openSettings();
                            }
                        },
                    },
                    {
                        text: 'Cancel',
                    },
                ],
                {
                    userInterfaceStyle: 'light',
                }
            );
            return;
        }

        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: 'images',
            allowsEditing: true,
            quality: 1,
        });

        if (!result.canceled && result.assets[0].uri) {
            updateDoc(docKey, side, result.assets[0].uri);
            setSheetVisible(false);
            setIsReupload(false);
            bottomSheetRef.current?.close();
        }
    };

    const renderUploadBox = (side: 'front' | 'back') => (
        <Pressable
            onPress={() => {
                setSelectedSide(side);
                setIsReupload(!!docUploads?.[docKey]?.[side]);
                setSheetVisible(true);
                if (Keyboard.isVisible()) {
                    Keyboard.dismiss();
                }
            }}
            className="h-[120px] w-[49%] items-center justify-center rounded-xl border border-border mid-lg:h-[250px]">
            {docUploads?.[docKey]?.[side] ? (
                <Image
                    source={{ uri: docUploads[docKey]?.[side] }}
                    className="h-full w-full rounded-xl"
                    resizeMode="cover"
                />
            ) : (
                <View className="items-center gap-3">
                    <Feather name="upload" size={30} color="#1F7575" />
                    <Text style={fonts.fontMedium} className="mt-2 text-text14 text-secondary">
                        {side === 'front' ? t('onboarding.doc.front') : t('onboarding.doc.back')}
                    </Text>
                </View>
            )}
        </Pressable>
    );

    const renderUploadFields = () => {
        switch (docKey) {
            case 'aadhar':
            case 'license':
                return (
                    <View className="mt-4 flex-row justify-between">
                        {renderUploadBox('front')}
                        {renderUploadBox('back')}
                    </View>
                );
            case 'pan':
            case 'rc':
                return <View className="mt-4 items-start">{renderUploadBox('front')}</View>;
            default:
                return null;
        }
    };

    const handleValidationStatus = async (text: string) => {
        const isValid = await trigger('number' as keyof FormData);
        const originalIndex = documents.findIndex((doc) => doc.value === docKey);

        if (!isValid && originalIndex !== -1 && documents[originalIndex].status === 'completed') {
            updateDocumentStatus(originalIndex, 'pending');
        }
    };

    const checkImagesUploaded = (): boolean => {
        if (docKey === 'pan' || docKey === 'rc' || docKey === 'selfie') {
            return !!docUploads?.[docKey]?.front;
        }
        if (docKey === 'aadhar' || docKey === 'license') {
            return !!docUploads?.[docKey]?.front && !!docUploads?.[docKey]?.back;
        }
        return false;
    };

    const onSubmit = (data: FormData) => {
        if (!checkImagesUploaded()) {
            Alert.alert(
                'Upload Required',
                `Please upload required images for your ${cardName}.`,
                [
                    {
                        text: 'OK',
                    },
                ],
                {
                    userInterfaceStyle: 'light',
                }
            );
            return;
        }
        const originalIndex = documents.findIndex((doc) => doc.value === docKey);
        if (originalIndex !== -1) {
            updateDocumentStatus(originalIndex, 'completed');
        }
        router.back();
    };

    const placeholder =
        cardName === t('onboarding.doc.docName.license')
            ? t('onboarding.doc.inputPlaceholder.driving')
            : cardName === t('onboarding.doc.docName.rc')
              ? t('onboarding.doc.inputPlaceholder.vehicle')
              : t('onboarding.doc.inputPlaceholder.default', { cardName });

    return (
        <View className="flex-1 bg-white">
            <TitleHeader
                backArrow="chevron-left"
                stepPregress={{
                    currentStep: [1, 1, completedDocsCount, 0],
                    totalNumberOfSteps: [1, 1, 5, 0],
                    height: 10,
                    splitInto: 4,
                }}
                onPressBack={() => {
                    if (cameraVisible) closeCamera();
                    else router.back();
                }}
            />

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                    contentContainerStyle={{ flexGrow: 1 }}
                    style={{ flex: 1 }}>
                    {docKey === 'selfie' ? (
                        <DocCamera
                            type="selfie"
                            view="front"
                            onCapture={(uri) => {
                                updateDoc('selfie', 'front', uri);
                                const originalIndex = documents.findIndex(
                                    (doc) => doc.value === 'selfie'
                                );
                                if (originalIndex !== -1) {
                                    updateDocumentStatus(originalIndex, 'completed');
                                }
                            }}
                            onClose={() => router.back()}
                        />
                    ) : cameraVisible ? (
                        <Animated.View
                            style={[
                                {
                                    flex: 1,
                                    position: 'absolute',
                                    width: '100%',
                                    height: '100%',
                                },
                                animatedStyle,
                            ]}>
                            <DocCamera
                                type={cameraConfig.type as 'aadhar' | 'pan' | 'license' | 'rc'}
                                view={cameraConfig.view as 'front' | 'back'}
                                forDoc={cameraConfig.forDoc as 'aadhar' | 'pan' | 'license' | 'rc'}
                                cardName={cameraConfig.cardName}
                                onCapture={handleCapture}
                                onClose={closeCamera}
                            />
                        </Animated.View>
                    ) : (
                        <View className="mt-4 flex-[0.95] gap-6 px-primary">
                            <Text style={fonts.fontSemiBold} className="text-text18 text-secondary">
                                {t('onboarding.doc.provideDetails', {
                                    cardName,
                                })}
                            </Text>

                            <Controller
                                control={control}
                                // @ts-expect-error
                                name="number"
                                render={({ field: { onChange, onBlur, value } }) => (
                                    // @ts-expect-error
                                    <InputField
                                        label={t('onboarding.doc.inputNumber', {
                                            cardName,
                                        })}
                                        placeholder={placeholder}
                                        placeholderLineLenth={1}
                                        placeholderEllipseMode="tail"
                                        value={value}
                                        onChangeText={(text) => {
                                            const formattedText = formatDocumentNumber(
                                                text,
                                                docKey
                                            );
                                            onChange(formattedText);
                                            updateDocNumber(
                                                docKey,
                                                sanitizeDocumentNumber(formattedText, docKey)
                                            );
                                            handleValidationStatus(formattedText);
                                        }}
                                        onBlur={onBlur}
                                        // @ts-expect-error
                                        errorMsg={errors.number?.message}
                                        autoCapitalize="characters"
                                        {...getInputProps(docKey, value)}
                                    />
                                )}
                            />

                            <Text style={fonts.fontSemiBold} className="text-text16 text-secondary">
                                {t('onboarding.doc.uploadImage', { cardName })}
                                {docKey === 'aadhar' || docKey === 'license' ? 's' : ''}
                            </Text>

                            {renderUploadFields()}
                            <View className="flex-1" />

                            <CustomBtn
                                title={t('onboarding.doc.submit')}
                                onPress={handleSubmit(onSubmit)}
                                btnStyle={{ bottom: -12 }}
                            />
                        </View>
                    )}
                </ScrollView>
            </KeyboardAvoidingView>

            <UploadOptionsSheet
                isVisible={sheetVisible}
                onSelectCamera={() => openCamera(selectedSide)}
                onSelectFile={() => pickImage(selectedSide)}
                onClose={() => {
                    setSheetVisible(false);
                    bottomSheetRef.current?.close();
                }}
                reff={bottomSheetRef}
                isReupload={isReupload}
                setIsReupload={setIsReupload}
            />
        </View>
    );
};

export default Docs;
