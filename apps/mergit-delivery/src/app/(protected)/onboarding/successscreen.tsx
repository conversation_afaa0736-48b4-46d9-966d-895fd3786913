import { View, Text } from 'react-native';
import React, { useEffect, useContext } from 'react';
import { SuccessFailure } from '@mergit-mobile/components';
import { fonts } from '@mergit-mobile/styles';
import { RelativePathString, router, useLocalSearchParams } from 'expo-router';
import { useOnboardingStore } from '~/services/onbording';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '~/services/auth';

const SuccessScreen = () => {
    const { t } = useTranslation();
    const { completedScreen, routeTo = '/onboarding' } = useLocalSearchParams<{
        completedScreen: string;
        routeTo?: string;
    }>();

    const { completeStep } = useOnboardingStore();
    const { login } = useAuthStore();

    const stepId =
        completedScreen === 'Profile Details'
            ? 1
            : completedScreen === 'Work Details'
              ? 2
              : completedScreen === 'Document'
                ? 3
                : 4;

    useEffect(() => {
        const timer = setTimeout(() => {
            if (completedScreen === 'Verification Completed') {
                login('token'); // simulate login
            } else {
                router.replace(routeTo as RelativePathString);
            }
            completeStep(stepId); // mark onboarding step complete
        }, 3000);

        return () => clearTimeout(timer);
    }, [completedScreen, routeTo]);

    const getSubTitle = () => {
        if (completedScreen === 'Order Delivery Kit') {
            return t('payment_success');
        }
        if (completedScreen === 'Verification Completed') {
            return '';
        }
        return t('successfully_completed');
    };

    return (
        <View className="flex-1 items-center justify-center gap-3 bg-white">
            <SuccessFailure status="success" iconSize={30} />
            <Text style={fonts.fontMedium} className="mt-3 text-text16 text-secondary">
                {completedScreen === 'Order Delivery Kit'
                    ? t('onboarding.successScreen.paymentSuccess')
                    : completedScreen === 'Verification Completed'
                      ? ''
                      : t('onboarding.successScreen.successMessage')}
            </Text>
            <Text style={fonts.fontBold} className="text-text20 text-secondary">
                {completedScreen}
            </Text>
        </View>
    );
};

export default SuccessScreen;
