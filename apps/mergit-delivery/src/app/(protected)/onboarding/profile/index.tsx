import { View, ScrollView, Dimensions } from 'react-native';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { BankDetails, BasicDetailForm } from '~/components/onboarding';
import { VerifyEmailMode } from '~/components/onboarding';
import { TitleHeader } from 'components/common';
import Animated, { useSharedValue, withTiming, useAnimatedStyle } from 'react-native-reanimated';
import { router } from 'expo-router';
import { basicdetailSchema, useBasicDetailsStore } from '~/services/onbording';
import { useTabsStore } from '~/services/tabs';

const SCREEN_WIDTH = Dimensions.get('window').width;

const BasicDetails = () => {
    const [gender, setGender] = useState('');
    const [maritalStatus, setMaritalStatus] = useState('');
    const [physicallyChallanged, setPhysicallyChallanged] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const { formData, updateField } = useBasicDetailsStore();
    const { bottomInset } = useTabsStore();

    const [currentStep, setCurrentStep] = useState<'basic' | 'bank'>('basic');
    const translateX = useSharedValue(0);

    const goToStep = (step: 'basic' | 'bank') => {
        const direction = step === 'basic' ? -1 : 1;
        translateX.value = withTiming(direction * -SCREEN_WIDTH, {
            duration: 300,
        });
        setTimeout(() => {
            translateX.value = withTiming(0, { duration: 300 });
            setCurrentStep(step);
        }, 300);
    };

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateX: translateX.value }],
    }));

    const onSubmit = () => {
        setIsOpen(true);
    };

    const onComplete = () => {
        router.push({
            pathname: '/onboarding/successscreen',
            params: { completedScreen: 'Profile Details' },
        });
    };

    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
    } = useForm({
        defaultValues: {
            name: formData['name'] ?? '',
            email: formData['email'] ?? '',
            gender: formData['gender'] ?? '',
            maritalStatus: formData['maritalStatus'] ?? '',
            physicallyChallanged: formData['physicallyChallanged'] ?? '',
        },
        resolver: yupResolver(basicdetailSchema),
    });

    const renderStep = () => {
        switch (currentStep) {
            case 'basic':
                return (
                    <BasicDetailForm
                        control={control}
                        setValue={setValue}
                        errors={errors}
                        formData={formData}
                        updateField={updateField}
                        gender={gender}
                        setGender={setGender}
                        maritalStatus={maritalStatus}
                        setMaritalStatus={setMaritalStatus}
                        physicallyChallanged={physicallyChallanged}
                        setPhysicallyChallanged={setPhysicallyChallanged}
                        onPress={handleSubmit(onSubmit)}
                    />
                );
            case 'bank':
                return (
                    <BankDetails
                        formData={formData}
                        updateField={updateField}
                        onComplete={onComplete}
                    />
                );
            default:
                return null;
        }
    };

    const handleBack = () => {
        if (currentStep === 'bank') {
            goToStep('basic');
        } else {
            router.back();
        }
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader
                backArrow="chevron-left"
                onPressBack={handleBack}
                stepPregress={{
                    currentStep: [currentStep === 'basic' ? 1 : 2, 0, 0, 0],
                    totalNumberOfSteps: [2, 0, 0, 0],
                    height: 10,
                    splitInto: 4,
                }}
                nhIcon
            />

            <ScrollView
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                contentContainerStyle={{ flexGrow: 1, paddingBottom: bottomInset }}>
                <Animated.View
                    style={[
                        {
                            flex: 1,
                            paddingHorizontal: 16,
                            gap: 30,
                        },
                        animatedStyle,
                    ]}>
                    {renderStep()}
                </Animated.View>
            </ScrollView>

            <VerifyEmailMode
                isOpen={isOpen}
                onClose={() => setIsOpen(false)}
                email={formData['email']}
                setIsBasicFormCompleted={() => goToStep('bank')}
            />
        </View>
    );
};

export default BasicDetails;
