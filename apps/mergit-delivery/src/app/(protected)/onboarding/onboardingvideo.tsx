import { View, Text } from 'react-native';
import { TitleHeader } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { VideoList } from '~/components/onboarding';
import { videoThumbnail } from 'assets/images/onboring';
import { CustomBtn } from '@mergit-mobile/components';
import { router } from 'expo-router';
import { useVideoStore } from '~/services/onbording';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '~/services/auth';

const Onboardingvideo = () => {
    const { completedVideos } = useVideoStore();
    const { setOnBoradingComplete } = useAuthStore();
    const { t } = useTranslation();

    const steps = [
        {
            id: 1,
            title: t('onboarding.video.trainingVideo.videoTitle.1'),
            image: videoThumbnail,
            unlocked: true,
            completed: completedVideos[1],
            onPress: () =>
                router.push(
                    '/onboarding/video-screen?videoUrl=https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/cycling+1.mp4&videoId=1'
                ),
        },
        {
            id: 2,
            title: t('onboarding.video.trainingVideo.videoTitle.2'),
            image: videoThumbnail,
            unlocked: completedVideos[1],
            completed: completedVideos[2],
            onPress: () =>
                router.push(
                    '/onboarding/video-screen?videoUrl=https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/girlwalking+1.mp4&videoId=2'
                ),
        },
        {
            id: 3,
            title: t('onboarding.video.trainingVideo.videoTitle.3'),
            image: videoThumbnail,
            unlocked: completedVideos[2],
            completed: completedVideos[3],
            onPress: () =>
                router.push(
                    '/onboarding/video-screen?videoUrl=https://oneapp-development.s3.us-east-1.amazonaws.com/delivery_video/sunflower+1.mp4&videoId=3'
                ),
        },
    ];

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow="chevron-left" nhIcon />
            <View className="flex-1 gap-4 px-primary">
                <Text style={fonts.fontSemiBold} className="text-text18 text-secondary">
                    {t('onboarding.video.trainingVideo.titlte')}
                </Text>
                <Text style={fonts.fontMedium} className="text-text15 text-secondary">
                    {t('onboarding.video.trainingVideo.discription')}
                </Text>
                <Text style={fonts.fontSemiBold} className="text-text18 text-secondary">
                    {t('onboarding.video.trainingVideo.subTitlte')}
                </Text>

                {steps.map((step) => (
                    <VideoList key={step.id} step={step} />
                ))}
                <View className="flex-[0.9]" />
                <CustomBtn
                    title={t('continue')}
                    disabled={!completedVideos[3]}
                    onPress={() => {
                        router.push({
                            pathname: '/onboarding/successscreen',
                            params: {
                                completedScreen: 'Verification Completed',
                            },
                        });
                        setOnBoradingComplete(true);
                    }}
                />
            </View>
        </View>
    );
};

export default Onboardingvideo;
