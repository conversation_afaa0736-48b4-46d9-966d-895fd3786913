import React from 'react';
import { VideoPlayer } from 'components/common';
import { useLocalSearchParams } from 'expo-router';
import { useVideoStore } from '~/services/onbording';

const VideoScreen = () => {
    const { videoUrl, videoId } = useLocalSearchParams();
    const { markVideoComplete } = useVideoStore();

    return (
        <VideoPlayer
            source={videoUrl as string}
            onCompleted={() => {
                markVideoComplete(Number(videoId));
                console.log('completed');
            }}
        />
    );
};

export default VideoScreen;
