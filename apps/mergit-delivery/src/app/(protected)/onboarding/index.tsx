import { ScrollView, Text, View } from 'react-native';
import { TitleHeader } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import {
    aadhaar,
    deliveryKit,
    document,
    license,
    pan,
    profile,
    rc,
    video,
    work,
} from 'assets/images/onboring';
import { StepCard, VerificationCard } from '~/components/onboarding';
import { RelativePathString, router } from 'expo-router';
import { useOnboardingStore } from '~/services/onbording';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const stepImages = {
    1: profile,
    2: work,
    3: document,
    4: deliveryKit,
};

const OnboardingHome = () => {
    const { steps } = useOnboardingStore();
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const allCompleted = steps.every((step) => step.completed);

    const DOCUMENTS = [
        {
            name: t('onboarding.video.documents.aadhar'),
            icon: aadhaar,
            bgColor: '#FBE5B5',
            value: 'aadhar',
            status: 'completed',
        },
        {
            name: t('onboarding.video.documents.pan'),
            icon: pan,
            bgColor: '#EAFFF2',
            value: 'pan',
            status: 'completed',
        },
        {
            name: t('onboarding.video.documents.license'),
            icon: license,
            bgColor: '#FFE4E5',
            value: 'license',
            status: 'completed',
        },
        {
            name: t('onboarding.video.documents.rc'),
            icon: rc,
            bgColor: '#E4F1FF',
            value: 'rc',
            status: 'completed',
        },
        {
            name: t('onboarding.video.documents.video'),
            icon: video,
            bgColor: '#FFD6D4',
            value: 'video',
            status: 'pending',
        },
    ];

    const getDescription = (id: number) => {
        switch (id) {
            case 1:
                return 'onboarding.description.1';
            case 2:
                return 'onboarding.description.2';
            case 3:
                return 'onboarding.description.3';
            case 4:
                return 'onboarding.description.4';
            default:
                return '';
        }
    };

    const getRoute = (id: number) => {
        switch (id) {
            case 1:
                return '/profile';
            case 2:
                return '/work';
            case 3:
                return '/document';
            case 4:
                return '/delivery-kit';
            default:
                return '';
        }
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader nhIcon />

            <ScrollView
                contentContainerClassName="flex-grow"
                contentContainerStyle={{ paddingBottom: bottomInset }}
                showsVerticalScrollIndicator={false}>
                {!allCompleted ? (
                    <View className="flex-1 gap-4 px-primary">
                        <Text
                            style={fonts.fontSemiBold}
                            className="text-text17 tracking-wide text-secondary">
                            {t('onboarding.heading')}
                        </Text>

                        <View className="mt-5 gap-3">
                            {steps.map((step) => (
                                <StepCard
                                    key={step.id}
                                    step={{
                                        ...step,
                                        description: getDescription(step.id),
                                        image: stepImages[step.id as keyof typeof stepImages],
                                        onPress: () =>
                                            router.push(
                                                `/onboarding${getRoute(step.id)}` as RelativePathString
                                            ),
                                    }}
                                />
                            ))}
                        </View>
                    </View>
                ) : (
                    <View className="flex-1 gap-4 px-primary">
                        <Text
                            style={fonts.fontSemiBold}
                            className="text-text18 tracking-wide text-secondary">
                            {t('onboarding.verification')}
                        </Text>
                        <Text
                            style={fonts.fontMedium}
                            className="text-text15 tracking-wide text-secondary">
                            {t('onboarding.verificationDiscription')}
                        </Text>

                        {DOCUMENTS.sort((a, b) => {
                            if (
                                (a.status === 'pending' && b.status === 'completed') ||
                                a.status === 'rejected'
                            )
                                return -1;
                            if (
                                (a.status === 'completed' && b.status === 'pending') ||
                                a.status === 'rejected'
                            )
                                return 1;
                            return 0;
                        }).map((doc, index) => (
                            <VerificationCard
                                key={doc.value}
                                item={doc as any}
                                index={index}
                                handleDocumentPress={(value) =>
                                    router.push(
                                        `/onbording/document/${value}` as RelativePathString
                                    )
                                }
                            />
                        ))}
                    </View>
                )}
            </ScrollView>
        </View>
    );
};

export default OnboardingHome;
