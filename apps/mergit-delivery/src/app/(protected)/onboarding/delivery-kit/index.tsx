import { View, Dimensions, ScrollView, Platform } from 'react-native';
import React, { useState } from 'react';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { router } from 'expo-router';
import { TitleHeader } from 'components/common';
import { Address, Payment, Tshirt } from '~/components/onboarding';
import { useBasicDetailsStore } from '~/services/onbording';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

const SCREEN_WIDTH = Dimensions.get('window').width;

const DeliveryKit = () => {
    const [step, setStep] = useState<'tshirt' | 'address' | 'payment'>('tshirt');
    const translateX = useSharedValue(0);
    const { formData, updateField } = useBasicDetailsStore();
    const [cameFromPayment, setCameFromPayment] = useState(false);

    const goToStep = (nextStep: typeof step, direction: 'forward' | 'backward') => {
        const toValue = direction === 'forward' ? -SCREEN_WIDTH : SCREEN_WIDTH;

        translateX.value = withTiming(toValue, { duration: 300 });

        setTimeout(() => {
            setStep(nextStep);
            translateX.value = -toValue;
            translateX.value = withTiming(0, { duration: 300 });
        }, 300);
    };

    const goForward = (nextStep: typeof step) => goToStep(nextStep, 'forward');
    const goBackStep = () => {
        if (step === 'address') goToStep('tshirt', 'backward');
        else if (step === 'payment') goToStep('address', 'backward');
        else router.back();
    };

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateX: translateX.value }],
    }));

    return (
        <View className="flex-1 bg-white">
            <TitleHeader
                backArrow="chevron-left"
                nhIcon
                onPressBack={goBackStep}
                stepPregress={{
                    currentStep: [1, 1, 1, step === 'tshirt' ? 1 : step === 'address' ? 2 : 3],
                    totalNumberOfSteps: [1, 1, 1, 3],
                    height: 10,
                    splitInto: 4,
                }}
            />

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ flexGrow: 1 }}
                    className="flex-1"
                    keyboardShouldPersistTaps="handled">
                    <Animated.View style={[{ flex: 1 }, animatedStyle]}>
                        {step === 'tshirt' && (
                            <Tshirt
                                updateField={updateField}
                                formData={formData}
                                onNext={() => {
                                    if (cameFromPayment) {
                                        setCameFromPayment(false);
                                        goForward('payment');
                                    } else {
                                        goForward('address');
                                    }
                                }}
                            />
                        )}
                        {step === 'address' && (
                            <Address
                                updateField={updateField}
                                formData={formData}
                                onNext={() => goForward('payment')}
                            />
                        )}
                        {step === 'payment' && (
                            <Payment
                                formData={formData}
                                onNext={() =>
                                    router.push({
                                        pathname: '/onbording/successscreen',
                                        params: {
                                            completedScreen: 'Order Delivery Kit',
                                        },
                                    })
                                }
                                editAddress={() => goToStep('address', 'backward')}
                                onChangeSize={() => {
                                    setCameFromPayment(true);
                                    goToStep('tshirt', 'backward');
                                }}
                            />
                        )}
                    </Animated.View>
                </ScrollView>
            </KeyboardAvoidingView>
        </View>
    );
};

export default DeliveryKit;
