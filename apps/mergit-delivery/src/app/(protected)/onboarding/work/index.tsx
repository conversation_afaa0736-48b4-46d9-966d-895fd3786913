import React, { useState } from 'react';
import { View, Dimensions } from 'react-native';
import { TitleHeader } from 'components/common';
import { WorkIn, DeliveryType, Timing, Vechile } from '~/components/onboarding';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { router } from 'expo-router';

const SCREEN_WIDTH = Dimensions.get('window').width;

const WorkDetails = () => {
    const [step, setStep] = useState<'workin' | 'delivery' | 'timing' | 'vehicle'>('workin');
    const translateX = useSharedValue(0);

    const goToStep = (nextStep: typeof step, direction: 'forward' | 'backward') => {
        const toValue = direction === 'forward' ? -SCREEN_WIDTH : SCREEN_WIDTH;

        translateX.value = withTiming(toValue, { duration: 300 });

        setTimeout(() => {
            setStep(nextStep);
            translateX.value = -toValue;
            translateX.value = withTiming(0, { duration: 300 });
        }, 300);
    };

    const goForward = (nextStep: typeof step) => goToStep(nextStep, 'forward');
    const goBackStep = () => {
        if (step === 'delivery') goToStep('workin', 'backward');
        else if (step === 'timing') goToStep('delivery', 'backward');
        else if (step === 'vehicle') goToStep('timing', 'backward');
        else router.back();
    };

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateX: translateX.value }],
    }));

    return (
        <View className="flex-1 bg-white">
            <TitleHeader
                backArrow="chevron-left"
                nhIcon
                onPressBack={goBackStep}
                stepPregress={{
                    currentStep: [
                        1,
                        step === 'workin' ? 1 : step === 'delivery' ? 2 : step === 'timing' ? 3 : 4,
                        0,
                        0,
                        0,
                    ],
                    totalNumberOfSteps: [1, 4, 0, 0],
                    height: 10,
                    splitInto: 4,
                }}
            />

            <Animated.View style={[{ flex: 1, padding: 20 }, animatedStyle]}>
                {step === 'workin' && <WorkIn onNext={() => goForward('delivery')} />}
                {step === 'delivery' && <DeliveryType onNext={() => goForward('timing')} />}
                {step === 'timing' && <Timing onNext={() => goForward('vehicle')} />}
                {step === 'vehicle' && (
                    <Vechile
                        onNext={() =>
                            router.push({
                                pathname: '/onboarding/successscreen',
                                params: { completedScreen: 'Work Details' },
                            })
                        }
                    />
                )}
            </Animated.View>
        </View>
    );
};

export default WorkDetails;
