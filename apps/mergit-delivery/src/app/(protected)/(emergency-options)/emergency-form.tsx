import React, { useEffect, useState } from 'react';
import {
    View,
    KeyboardAvoidingView,
    Platform,
    Text,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
} from 'react-native';
import { InputField, CustomBtn } from '@mergit-mobile/components';
import { CustomText, TitleHeader } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { router, useLocalSearchParams } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { emergencyContactSchema, useEmergencyStore } from 'services/emergency';
import { useTabsStore } from '~/services/tabs';

interface GenderOption {
    id: number;
    label: string;
    value: string;
}

const genderOptions: GenderOption[] = [
    { id: 1, label: 'Spouse', value: 'Spouse' },
    { id: 2, label: 'Children', value: 'Children' },
    { id: 3, label: 'Parents', value: 'Parents' },
    { id: 4, label: 'Friend', value: 'Friend' },
    { id: 5, label: 'Others', value: 'Others' },
];

const EmergencyForm = () => {
    const { contactId } = useLocalSearchParams();
    const { contacts, addContact, updateContact } = useEmergencyStore();

    const [isFormEditable, setIsFormEditable] = useState(true);
    const [isEditing, setIsEditing] = useState(false);

    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
    } = useForm({
        defaultValues: {
            fullName: '',
            phoneNumber: '',
            selectedGender: '',
        },
        resolver: yupResolver(emergencyContactSchema),
    });

    useEffect(() => {
        if (contactId) {
            const contact = contacts.find((c) => c.id === contactId);
            if (contact) {
                setValue('fullName', contact.fullName);
                setValue('phoneNumber', contact.phoneNumber);
                setValue('selectedGender', contact.gender);
                setIsEditing(true);
                setIsFormEditable(false);
            }
        }
    }, [contactId]);

    const onSubmit = (data: any) => {
        const contactData = {
            fullName: data.fullName,
            phoneNumber: data.phoneNumber,
            gender: data.selectedGender,
        };

        if (isEditing && contactId) {
            updateContact(contactId as string, contactData);
        } else {
            addContact(contactData);
        }
        router.back();
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader
                shadowBottom
                backArrow
                title={t(isEditing ? 'edit_contact' : 'add_contact')}
            />

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1, gap: 20 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ flexGrow: 1 }}
                    keyboardShouldPersistTaps="handled">

                    <View
                        className='flex-1 px-primary gap-8 pt-7'
                        style={{ opacity: isFormEditable ? 1 : 0.8 }}>

                        <View>
                            <Controller
                                control={control}
                                name="fullName"
                                render={({ field: { onChange, value } }) => (
                                    <InputField
                                        label="Full Name"
                                        placeholder="Enter Full Name"
                                        value={value}
                                        onChangeText={onChange}
                                        disabled={!isFormEditable}
                                        animate={false}
                                        errorMsg={errors.fullName?.message}
                                        autoCapitalize='words'
                                        inputMode='text'
                                        keyboardType='default'
                                        maxLength={35}
                                    />
                                )}
                            />
                        </View>

                        <View>
                            <Controller
                                control={control}
                                name="phoneNumber"
                                render={({ field: { onChange, value } }) => (
                                    <InputField
                                        label="Phone Number"
                                        placeholder="Enter Phone Number"
                                        value={value}
                                        onChangeText={onChange}
                                        keyboardType="numeric"
                                        maxLength={10}
                                        disabled={!isFormEditable}
                                        animate={false}
                                        errorMsg={errors.phoneNumber?.message}
                                    />
                                )}
                            />
                        </View>

                        <View className='gap-2'>
                            <CustomText
                                tKey="relationship"
                                style={fonts.fontSemiBold}
                                className="text-text16 tracking-wider text-secondary"
                            />

                            <Controller
                                control={control}
                                name="selectedGender"
                                render={({ field: { value, onChange } }) => (
                                    <>
                                        <View>
                                            {genderOptions.map((option) => {
                                                const selected = value === option.value;
                                                return (
                                                    <TouchableOpacity
                                                        key={option.id}
                                                        style={styles.option}
                                                        onPress={() => {
                                                            if (isFormEditable) {
                                                                onChange(option.value);
                                                            }
                                                        }}
                                                        activeOpacity={0.9}>
                                                        <View
                                                            style={[
                                                                styles.outerCircle,
                                                                selected && styles.selectedOuter,
                                                            ]}>
                                                            {selected && (
                                                                <View style={styles.innerCircle} />
                                                            )}
                                                        </View>
                                                        <Text style={styles.label}>{option.label}</Text>
                                                    </TouchableOpacity>
                                                );
                                            })}
                                        </View>
                                        {errors.selectedGender?.message && (
                                            <Text
                                                style={{
                                                    color: '#F04438',
                                                    fontSize: 14,
                                                    fontFamily: 'DMSans-Medium',
                                                    letterSpacing: 0.2,
                                                }}>
                                                {errors.selectedGender.message}
                                            </Text>
                                        )}
                                    </>
                                )}
                            />
                        </View>
                    </View>
                </ScrollView>
                <View className="bg-white px-primary" style={{ paddingBottom: bottomInset }}>
                    {isEditing && !isFormEditable ? (
                        <CustomBtn
                            title={t('edit')}
                            onPress={() => setIsFormEditable(true)}
                            icon={
                                <MaterialCommunityIcons
                                    name="square-edit-outline"
                                    size={24}
                                    color="white"
                                />
                            }
                            position="left"
                        />
                    ) : (
                        <CustomBtn
                            title={t(isEditing ? 'confirm' : 'save_contact')}
                            onPress={handleSubmit(onSubmit)}
                        />
                    )}
                </View>
            </KeyboardAvoidingView>

        </View>
    );
};

export default EmergencyForm;

const styles = StyleSheet.create({
    option: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 7,
    },
    outerCircle: {
        height: 20,
        width: 20,
        borderRadius: 10,
        borderWidth: 2,
        borderColor: '#0A0D12',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    selectedOuter: {
        borderColor: '#1F7575',
    },
    innerCircle: {
        height: 10,
        width: 10,
        borderRadius: 5,
        backgroundColor: '#1F7575',
    },
    label: {
        fontSize: 16,
        color: '#0A0D12',
        ...fonts.fontMedium,
        letterSpacing: 0.3,
    },
});
