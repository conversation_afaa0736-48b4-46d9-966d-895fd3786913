import { View } from 'react-native';
import { CustomText, TitleHeader } from 'components/common';
import { EmergencyListCard } from 'components/emergency-options';
import { fonts } from '@mergit-mobile/styles';
import { Ionicons } from '@expo/vector-icons';
import { CustomBtn, SuccessFailure, useBottomSheet } from '@mergit-mobile/components';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const EmergencyOptions = () => {
    const { t } = useTranslation();
    const { showBottomSheet, hideBottomSheet } = useBottomSheet();
    const { bottomInset } = useTabsStore();

    const handleCallPress = () => {
        hideBottomSheet();
        showBottomSheet(() => (
            <View className="w-full items-center gap-4 px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
                <SuccessFailure
                    outerWidth={90}
                    outerHeight={90}
                    innerWidth={55}
                    status="success"
                    innerHeight={55}
                    iconSize={30}
                />
                <CustomText
                    tKey="thank_you"
                    style={fonts.fontSemiBold}
                    className="text-text17 tracking-wide text-secondary"
                />

                <CustomText
                    tKey="team_call_shortly"
                    style={fonts.fontRegular}
                    className="text-text15 text-grayText tracking-wide"
                />

                <View className="w-full">
                    <CustomBtn
                        onPress={hideBottomSheet}
                        title={t('okay')}
                    />
                </View>
            </View>
        ));
    };

    const openBottomSheet = () => {
        showBottomSheet(() => (
            <View className="px-primary pt-primary gap-6" style={{ paddingBottom: bottomInset }}>
                <View className='gap-2'>
                    <CustomText
                        tKey="emergency_call"
                        style={fonts.fontSemiBold}
                        className="text-text17 text-secondary tracking-wide"
                    />
                    <CustomText
                        tKey="confirm_help_request"
                        style={fonts.fontRegular}
                        className="text-text15 text-grayText tracking-wide"
                    />
                </View>

                <View className="gap-3">
                    <CustomBtn
                        onPress={handleCallPress}
                        title={t('yes_i_need_call')}
                        icon={<Ionicons name="call-outline" size={18} color="#fff" />}
                    />
                    <CustomBtn
                        onPress={hideBottomSheet}
                        title={t('close')}
                        backgroundColor="#fff"
                        textStyle={{
                            color: '#1F7575',
                        }}
                    />
                </View>
            </View>
        ), 'hide')
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow shadowBottom title={t('emergency_options')} />

            <View className="flex-1 px-primary pt-4">
                <EmergencyListCard openBottomSheet={openBottomSheet} />
            </View>
        </View>
    );
};

export default EmergencyOptions;
