import { Text, View, TouchableOpacity, ScrollView } from 'react-native';
import { TitleHeader } from 'components/common';
import { CustomBtn } from '@mergit-mobile/components';
import { MaterialIcons, Feather } from '@expo/vector-icons';
import { router } from 'expo-router';
import { fonts } from '@mergit-mobile/styles';
import { useTranslation } from 'react-i18next';
import { useEmergencyStore } from 'services/emergency';
import { useTabsStore } from '~/services/tabs';

const ContactList = () => {
    const { contacts } = useEmergencyStore();
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1 bg-white">
            <TitleHeader shadowBottom backArrow title={t('emergency_contacts')} />

            <ScrollView showsVerticalScrollIndicator={false} contentContainerClassName="flex-grow p-primary gap-4">
                {contacts.map((contact) => (
                    <TouchableOpacity
                        activeOpacity={0.9}
                        key={contact.id}
                        className="flex-row items-center justify-between rounded-lg border border-border px-[12px] py-[16px]"
                        onPress={() =>
                            router.push({
                                pathname: '/emergency-form',
                                params: { contactId: contact.id },
                            })
                        }>
                        <View>
                            <View className="flex-row items-center gap-1">
                                <Text style={fonts.fontBold} className="text-lg">
                                    {contact.fullName}
                                </Text>
                                <Text className="text-lg">({contact.gender})</Text>
                            </View>
                            <View className="mt-2">
                                <Text className="text-lg">{contact.phoneNumber}</Text>
                            </View>
                        </View>
                        <Feather name="chevron-right" size={24} color="black" />
                    </TouchableOpacity>
                ))}
            </ScrollView>

            <View className="px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
                <CustomBtn
                    onPress={() => router.push('/emergency-form')}
                    title={t('add_emergency_contact')}
                    icon={<MaterialIcons name="add" size={24} color="#fff" />}
                />
            </View>
        </View>
    );
};

export default ContactList;
