import { ScrollView, View } from 'react-native';
import { useState } from 'react';
import { TitleHeader } from 'components/common';
import { TabSwitcher } from '@mergit-mobile/components';
import { IncentiveCard } from 'components/incentives';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

type IncentiveTab = 'open' | 'completed' | 'expired';

const Incentive = () => {
    const tabs = [
        { label: 'Open', value: 'open' },
        { label: 'Completed', value: 'completed' },
        { label: 'Expired', value: 'expired' },
    ];

    const [activeTab, setActiveTab] = useState(tabs[0].value);
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1">
            <TitleHeader
                title={t('incentives')}
                backArrow
                shadowBottom={false}
            />

            <View className="bg-white pt-2">
                <TabSwitcher tabs={tabs} activeTab={activeTab} onChange={setActiveTab} />
            </View>

            <ScrollView contentContainerClassName='flex-grow px-primary pt-primary gap-4' contentContainerStyle={{ paddingBottom: bottomInset }} showsVerticalScrollIndicator={false}>
                {Array.from({ length: 5 }).map((item, index) => (
                    <IncentiveCard
                        key={index}
                        showChevron={false}
                        status={activeTab as IncentiveTab}
                    />
                ))}
            </ScrollView>
        </View>
    );
};

export default Incentive;
