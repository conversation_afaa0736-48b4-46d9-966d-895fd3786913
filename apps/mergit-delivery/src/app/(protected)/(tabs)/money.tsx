import { Pressable, StyleSheet, View } from 'react-native';
import { CustomText, TitleHeader } from 'components/common';
import { LinearGradient } from 'expo-linear-gradient';
import { fonts } from '@mergit-mobile/styles';
import { Image } from 'expo-image';
import { colors } from 'utils/constants';
import { Ionicons } from '@expo/vector-icons';
import { EarningsIcon } from 'assets/icons/money';
import { router } from 'expo-router';

const Money = () => {
    return (
        <View className="flex-1 bg-background">
            <TitleHeader
                showSwitch
                enhIcon
                onPressHelpandsupport={() => router.push('/(protected)/profile/(help-and-support)')}
            />

            <LinearGradient colors={['#86BFBF', '#CBF1F14D']} style={styles.linearGradient}>
                <View className="w-full flex-row items-center justify-between">
                    <CustomText
                        tKey="money_hub"
                        style={fonts.fontSemiBold}
                        className="text-text22 tracking-wide text-primary"
                    />
                    <Image
                        source={require('assets/images/money-tab/safetylocker.png')}
                        style={{ width: 180, height: 180 }}
                        contentFit="cover"
                    />
                </View>
            </LinearGradient>

            <View className="flex-row items-center gap-3 p-primary">
                <Pressable
                    onPress={() => router.push('/(protected)/(money)')}
                    className="h-36 flex-1 items-center justify-center gap-4 rounded-lg border border-border px-3">
                    <Ionicons name="wallet-outline" size={35} color={colors.primary} />
                    <CustomText
                        tKey="wallet"
                        style={fonts.fontMedium}
                        className="text-text15 tracking-wider text-secondary"
                    />
                </Pressable>

                <Pressable
                    onPress={() => router.push('/(protected)/(money)/earnings')}
                    className="h-36 flex-1 items-center justify-center gap-4 rounded-lg border border-border px-3">
                    <EarningsIcon />
                    <CustomText
                        tKey="earnings"
                        style={fonts.fontMedium}
                        className="text-text15 tracking-wider text-secondary"
                    />
                </Pressable>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    linearGradient: {
        width: '100%',
        paddingRight: 10,
        paddingLeft: 16,
        paddingVertical: 40,
        borderBottomLeftRadius: 12,
        borderBottomRightRadius: 12,
        borderWidth: 1,
        borderColor: '#D5D7DA',
    },
});

export default Money;
