import { useContext, useState } from 'react';
import { View, ScrollView, TouchableOpacity } from 'react-native';
import { CustomBottomSheet, CustomBtn } from '@mergit-mobile/components';
import { ProfileCard, ProfileMenuList } from 'components/profile';
import { fonts } from '@mergit-mobile/styles';
import { MaterialIcons } from '@expo/vector-icons';
import { CustomText, TitleHeader } from 'components/common';
import { router } from 'expo-router';
import { useLocationStore } from '~/services/location';
import { useOrderStore } from 'services/orders';
import { useTranslation } from 'react-i18next';
import { SocketContext } from 'context/SocketContext';
import { useProfileStore } from 'services/profile';
import { useAuthStore } from '~/services/auth';

const More = () => {
    const { t } = useTranslation();

    const { logout } = useAuthStore();
    const { socket } = useContext(SocketContext);

    const [isSheetVisible, setSheetVisible] = useState(false);

    const { setIsEnabled } = useLocationStore();
    const { setProfileDetails } = useProfileStore();
    const { updateOrders } = useOrderStore();

    const handleLogoutPress = () => {
        setSheetVisible(true);
    };

    const handleLogoutConfirm = () => {
        setSheetVisible(false);
        if (socket && socket.connected) {
            socket.disconnect();
        }
        updateOrders(null);
        logout();
        setIsEnabled(false);
        setProfileDetails(null);
    };

    return (
        <View className="flex-1">
            <TitleHeader
                showSwitch
                enhIcon
                onPressHelpandsupport={() => router.push('/(protected)/profile/(help-and-support)')}
            />

            <ScrollView
                contentContainerClassName='flex-grow p-primary gap-5'
                showsVerticalScrollIndicator={false}>
                <ProfileCard />

                <ProfileMenuList />

                <CustomBtn
                    title={t('logout')}
                    onPress={handleLogoutPress}
                    position="left"
                    icon={<MaterialIcons name="logout" size={22} color="#fff" />}
                />
            </ScrollView>

            <CustomBottomSheet isVisible={isSheetVisible} onClose={() => setSheetVisible(false)}>
                <View className="px-primary py-6">
                    <CustomText
                        tKey="logout_confirmation"
                        className="text-xl text-secondary"
                        style={fonts.fontSemiBold}
                    />

                    <CustomText
                        tKey="no_delivery_requests_until_login"
                        className="mt-2 text-lg text-[#414651]"
                    />

                    <View className="mt-4 flex-row justify-between gap-4">
                        <TouchableOpacity
                            activeOpacity={0.8}
                            className="flex-1 rounded-md border border-primary py-3"
                            onPress={() => setSheetVisible(false)}>
                            <CustomText
                                tKey="close"
                                className="text-center text-[#1F7575]"
                                style={fonts.fontMedium}
                            />
                        </TouchableOpacity>

                        <TouchableOpacity
                            activeOpacity={0.8}
                            className="flex-1 rounded-md bg-primary py-3"
                            onPress={handleLogoutConfirm}>
                            <View className="flex-row items-center justify-center gap-2">
                                <MaterialIcons name="logout" size={20} color="white" />
                                <CustomText
                                    tKey="logout"
                                    className="text-white"
                                    style={fonts.fontMedium}
                                />
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            </CustomBottomSheet>
        </View>
    );
};

export default More;
