import { <PERSON>, ScrollView } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { Greetings, LiveOrdersCard, ShiftCard, TodaysProgress } from 'components/home';
import { IncentiveCard } from 'components/incentives';
import { CustomText, TitleHeader } from 'components/common';
import { router } from 'expo-router';
import { useOrderStore } from 'services/orders';

const Home = () => {
    const { orders } = useOrderStore();

    return (
        <View className="flex-1 bg-background">
            <TitleHeader
                showSwitch
                enhIcon
                onPressHelpandsupport={() => router.push('/(protected)/profile/(help-and-support)')}
            />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerClassName="flex-grow py-primary">
                <View className="flex-1 gap-5">
                    <View className="gap-5 px-primary">
                        <Greetings />

                        <View className="h-px w-full bg-border" />

                        <CustomText
                            tKey="live_orders"
                            style={fonts.fontSemiBold}
                            className="text-text16 tracking-wide text-secondary"
                        />
                    </View>

                    <View className="gap-5">
                        {orders ? (
                            <View className="flex-1">
                                <LiveOrdersCard />
                            </View>
                        ) : (
                            <CustomText
                                tKey="no_orders_received"
                                style={fonts.fontRegular}
                                className="text-center text-text15 tracking-wide text-neutral"
                            />
                        )}
                    </View>

                    <View className="flex-1 gap-4 px-primary">
                        <ShiftCard />

                        <TodaysProgress />

                        <IncentiveCard showChevron={true} />
                    </View>
                </View>
            </ScrollView>

        </View>
    );
};

export default Home;
