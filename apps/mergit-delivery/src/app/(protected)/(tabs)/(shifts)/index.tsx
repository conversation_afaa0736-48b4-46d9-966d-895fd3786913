import { useState } from 'react';
import { View, ScrollView, Pressable, Text } from 'react-native';
import { CustomText, TitleHeader } from 'components/common';
import { ShiftTimings, SlotDates } from 'components/shifts';
import { fonts } from '@mergit-mobile/styles';
import { CustomBtn, SuccessFailure, useCustomModal } from '@mergit-mobile/components';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { shiftTimings } from '~/utils/mock';

type ShiftTab = 'All' | 'Opened' | 'Closed';

const SHIFT_TABS = ['All', 'Opened', 'Closed'];

const Shifts = () => {
    const { t } = useTranslation();

    const [selectedTab, setSelectedTab] = useState<ShiftTab>('All');
    const { showModal, hideModal } = useCustomModal();

    const [selectedShifts, setSelectedShifts] = useState<string[]>([]);
    const handleShiftSelection = (id: string) => {
        setSelectedShifts((prevSelected) =>
            prevSelected.includes(id)
                ? prevSelected.filter((shiftId) => shiftId !== id)
                : [...prevSelected, id]
        );
    };

    const handleOpenModal = () => {
        showModal(
            <View className="w-full items-center gap-5">
                <SuccessFailure
                    outerWidth={90}
                    outerHeight={90}
                    innerHeight={60}
                    innerWidth={60}
                    iconSize={25}
                    status="success"
                />
                <CustomText
                    tKey="shift_booked"
                    tOptions={{ count: selectedShifts.length }}
                    style={fonts.fontSemiBold}
                    className="text-text15 tracking-wider text-secondary"
                />

                <CustomBtn
                    title={t('continue')}
                    onPress={hideModal}
                    height={50}
                    btnStyle={{ width: '100%' }}
                />
            </View>
        );
    };

    return (
        <View className="flex-1 bg-background">
            <TitleHeader
                showSwitch
                enhIcon
                onPressHelpandsupport={() => router.push('/(protected)/profile/(help-and-support)')}
            />

            <ScrollView
                contentContainerClassName="flex-grow py-primary gap-6"
                showsVerticalScrollIndicator={false}>
                <SlotDates />

                <Animated.View
                    layout={LinearTransition.duration(800).damping(300)}
                    className="flex-1 gap-6 px-primary">
                    <View className="h-px w-full bg-border" />

                    <View className="flex-row items-center gap-5">
                        {SHIFT_TABS.map((tab, index) => (
                            <Pressable
                                onPress={() => setSelectedTab(tab as ShiftTab)}
                                key={index}
                                className={`rounded-lg border bg-white px-4 py-3 ${selectedTab === tab ? 'border-primary' : 'border-border'
                                    }`}>
                                <Text
                                    style={fonts.fontMedium}
                                    className={`text-text15 tracking-wider ${selectedTab === tab ? 'text-primary' : 'text-neutral'
                                        }`}>
                                    {t(`${tab.toLowerCase()}`)}
                                </Text>
                            </Pressable>
                        ))}
                    </View>

                    <ShiftTimings
                        selectedTab={selectedTab}
                        handleShiftSelection={handleShiftSelection}
                        shiftsData={shiftTimings}
                        selectedShifts={selectedShifts}
                    />

                </Animated.View>

                <View className="px-primary">
                    <CustomBtn
                        title={t('book_now')}
                        onPress={handleOpenModal}
                        height={50}
                        disabled={!selectedShifts.length}
                    />
                </View>
            </ScrollView>
        </View>
    );
};

export default Shifts;
