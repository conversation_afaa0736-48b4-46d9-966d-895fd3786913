import { FlatList, Pressable, ScrollView, Text, View } from 'react-native';
import { CustomText, TitleHeader } from 'components/common';
import { fonts, shadow } from '@mergit-mobile/styles';
import { Feather, Ionicons } from '@expo/vector-icons';
import dayjs from 'dayjs';
import { colors } from 'utils/constants';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { shiftTimings } from 'utils/mock';
import { CustomBtn, useCustomModal } from '@mergit-mobile/components';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const SlotHistory = () => {
    const today = dayjs();
    const { t } = useTranslation();
    const { showModal, hideModal } = useCustomModal();
    const { bottomInset } = useTabsStore();

    const bookedShifts = shiftTimings[0].data.filter((shift) => shift.isBooked);

    const renderSlot = ({ item, index }: any) => (
        <Animated.View
            entering={FadeInUp.duration(400).delay(100 * index)}
            className="flex-row items-center gap-3 p-primary">
            <Text style={fonts.fontMedium} className="text-text15 tracking-wider text-secondary">
                {item.title}
            </Text>
            <View className="flex-1 items-start">
                {item.isBooked && (
                    <View className="rounded-full border border-[#17B26A] bg-[#DCFAE6] px-4 py-[6px]">
                        <CustomText
                            tKey="booked"
                            style={fonts.fontMedium}
                            className="text-text14 tracking-wide text-[#067647]"
                        />
                    </View>
                )}
            </View>
            <Pressable onPress={handleShowModel}>
                <Ionicons name="close" size={24} color={colors.secondary} />
            </Pressable>
        </Animated.View>
    );

    const handleShowModel = () => {
        showModal(
            <View className="gap-3">
                <CustomText
                    tKey="cancel_slot"
                    style={fonts.fontSemiBold}
                    className="text-text17 tracking-wider text-secondary"
                />

                <CustomText
                    tKey="cancel_slot_confirmation"
                    style={fonts.fontRegular}
                    className="text-text15 tracking-wide text-neutral"
                />

                <View className="mt-1 flex-row items-center gap-3">
                    <CustomBtn
                        title={t('cancel')}
                        onPress={hideModal}
                        height={50}
                        borderColor={colors.primary}
                        backgroundColor="transparent"
                        btnStyle={{ flex: 1 }}
                        textStyle={{ color: colors.primary }}
                    />

                    <CustomBtn
                        title={t('confirm')}
                        onPress={hideModal}
                        height={50}
                        btnStyle={{ flex: 1 }}
                    />
                </View>
            </View>
        );
    };

    return (
        <View className="flex-1 bg-background">
            <TitleHeader backArrow title={t('slot_history')} shadowBottom />

            <ScrollView showsVerticalScrollIndicator={false} contentContainerClassName="flex-grow" contentContainerStyle={{ paddingBottom: bottomInset }}>
                <View className="flex-1 gap-6 p-primary">
                    <View
                        className="flex-row items-center gap-3 rounded-lg border border-border bg-white p-4"
                        style={shadow}>
                        <Feather name="calendar" size={24} color={colors.secondary} />
                        <CustomText
                            tKey="today_date"
                            tOptions={{ date: today.format('DD MMM') }}
                            className="flex-1 text-text15 tracking-wide text-secondary"
                            style={fonts.fontMedium}
                        />
                        <Feather name="chevron-down" size={24} color={colors.secondary} />
                    </View>

                    <View className="w-full overflow-hidden rounded-lg border border-border">
                        <Animated.View
                            entering={FadeInUp.duration(400).delay(100)}
                            className="flex-row items-center gap-4 rounded-t-lg bg-[#A9D8D8] px-4 py-4">
                            <Text
                                style={fonts.fontSemiBold}
                                className="flex-1 text-text16 tracking-wide text-secondary">
                                {today.format('DD-MM-YYYY')} ({today.format('dddd')})
                            </Text>
                            <Feather name="chevron-down" size={24} color={colors.secondary} />
                        </Animated.View>
                        <FlatList
                            data={bookedShifts}
                            renderItem={renderSlot}
                            keyExtractor={(item) => item.id}
                            scrollEnabled={false}
                            showsVerticalScrollIndicator={false}
                            ItemSeparatorComponent={() => (
                                <View className="h-px w-full bg-border" />
                            )}
                        />
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default SlotHistory;
