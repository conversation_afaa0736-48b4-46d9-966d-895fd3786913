import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { CustomText, TitleHeader } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { rupeeSymbol } from 'utils/constants';
import { useLocalSearchParams } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTabsStore } from '~/services/tabs';

const SpecificOrder = () => {
    const params = useLocalSearchParams();
    const { bottomInset } = useTabsStore();

    const order = JSON.parse(params.order as string);

    return (
        <View className="flex-1 bg-background">
            <TitleHeader
                backArrow
                titleLeft={order.shopName}
                shadowBottom
                borderBottom
                iconRight={
                    <TouchableOpacity activeOpacity={0.9}>
                        <MaterialCommunityIcons
                            name="dots-vertical"
                            size={24}
                            color="black"
                            className="absolute bottom-[-12px] right-0"
                        />
                    </TouchableOpacity>
                }
            />

            <ScrollView showsVerticalScrollIndicator={false} contentContainerClassName='flex-grow px-primary pt-primary gap-5' contentContainerStyle={{ paddingBottom: bottomInset }}>

                <View className="gap-2">
                    <CustomText tKey="order_id" style={fonts.fontMedium} className='text-text16 tracking-wider text-secondary' />

                    <View className="flex-row items-center gap-4">
                        <Text className="flex-1 text-text19 text-primary tracking-wide" style={fonts.fontSemiBold}>
                            {order.id}
                        </Text>
                        <View className="rounded-full border border-[#17B26A] bg-[#DCFAE6] px-2 py-[4px]">
                            <Text className="text-text14 tracking-wide text-[#067647]">{order.status}</Text>
                        </View>
                    </View>
                    <Text className="text-text15 text-secondary tracking-wide" style={fonts.fontMedium}>{order.date}</Text>
                </View>

                <View className="gap-5 rounded-lg border border-border bg-white p-4">
                    <View className='gap-2'>
                        <CustomText
                            tKey="order_earnings"
                            className="text-text16 tracking-wide text-secondary"
                            style={fonts.fontMedium}
                        />
                        <Text style={fonts.fontSemiBold} className='text-text15 tracking-wide text-secondary'>
                            {rupeeSymbol}
                            {order.amount}
                        </Text>
                    </View>

                    <View className='gap-2'>
                        <CustomText
                            tKey="order_total_distance"
                            className="text-text16 tracking-wide text-secondary"
                            style={fonts.fontMedium}
                        />

                        <Text style={fonts.fontSemiBold} className='text-text15 tracking-wide text-secondary'>4 km</Text>
                    </View>

                    <View className='gap-2'>
                        <CustomText tKey="time" className="text-text16 tracking-wide text-secondary"
                            style={fonts.fontMedium} />

                        <Text style={fonts.fontSemiBold} className='text-text15 tracking-wide text-secondary'>30 min</Text>
                    </View>
                </View>

                <View className="rounded-lg border border-border bg-white p-4 gap-4">
                    <View className='gap-2'>
                        <CustomText tKey="order_details" style={fonts.fontMedium} className='text-text15 tracking-wide text-secondary' />
                        <Text className='text-text16 tracking-wider text-secondary' style={fonts.fontSemiBold}>
                            {order.shopName}
                        </Text>
                    </View>

                    <View className="h-px w-full bg-border" />

                    <View className='gap-2'>
                        <Text style={fonts.fontRegular} className="text-text15 tracking-wide text-secondary">
                            Family Bucket Chicken Biryani (4-5 Pax) x 1
                        </Text>
                        <Text style={fonts.fontRegular} className='text-text15 tracking-wide text-secondary'>
                            Hyderabad Chicken Biryani (4-5 Pax) x 1
                        </Text>
                    </View>
                </View>

                <View className="rounded-lg border border-border bg-white p-4 gap-4">
                    <CustomText
                        tKey="shop_customer_details"
                        className='text-text16 tracking-wider text-secondary' style={fonts.fontSemiBold}
                    />

                    <View className="h-px w-full bg-border" />

                    <View className='gap-3'>
                        <View className='flex-row items-center gap-2'>
                            <CustomText tKey="shop_name" className='text-text15 tracking-wider text-secondary' style={fonts.fontMedium} />
                            <Text className='flex-1 text-text15 tracking-wider text-secondary' style={fonts.fontSemiBold}>{order.shopName}</Text>
                        </View>
                        <View className='flex-row items-center gap-2'>
                            <CustomText tKey="customer_name" className='text-text15 tracking-wider text-secondary' style={fonts.fontMedium} />
                            <Text className='flex-1 text-text15 tracking-wider text-secondary' style={fonts.fontSemiBold}>John Doe</Text>
                        </View>
                    </View>

                </View>
            </ScrollView>

        </View>
    );
};

export default SpecificOrder;
