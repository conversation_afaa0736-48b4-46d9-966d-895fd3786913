import { useMemo, useState } from 'react';
import { View, ScrollView, Text, TouchableOpacity, Modal } from 'react-native';
import { CustomText, TitleHeader } from 'components/common';
import { DoubleOrders, FilterOptions, FilterTab, SingleOrder } from 'components/earnings';
import { fonts } from '@mergit-mobile/styles';
import { Feather, Entypo } from '@expo/vector-icons';
import DatePicker from 'react-native-ui-datepicker';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';
import { useBottomSheet } from '@mergit-mobile/components';
import { Calendar } from '~/components/shifts';

type FilterType = 'daily' | 'weekly' | 'monthly';
type SheetType = 'weekly' | 'monthly';

const weekOptions = Array.from({ length: 6 }, (_, i) => {
    const start = dayjs().subtract(i, 'week').startOf('week');
    const end = dayjs().subtract(i, 'week').endOf('week');
    return `${start.format('D MMM')} - ${end.format('D MMM')}`;
});

const monthOptions = Array.from({ length: 6 }, (_, i) => {
    return dayjs().subtract(i, 'month').format('MMMM YYYY');
});

const OrderHistory = () => {
    const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);
    const [date, setDate] = useState(dayjs());

    const [selectedFilter, setSelectedFilter] = useState<FilterType>('daily');
    const [dateRange, setDateRange] = useState({
        daily: dayjs().format('D MMM'),
        weekly: weekOptions[0],
        monthly: monthOptions[0],
    });

    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();
    const { showBottomSheet, hideBottomSheet } = useBottomSheet();

    const handleDateRangeChange = (range: string, filter: FilterType) => {
        setDateRange((prev) => ({ ...prev, [filter]: range }));
        hideBottomSheet();
    };

    const getDisplayText = useMemo(() => {
        switch (selectedFilter) {
            case 'weekly':
                return `Week : ${dateRange.weekly}`;

            case 'monthly':
                return `Month : ${dateRange.monthly}`;

            case 'daily':
            default:
                const selectedDate = dayjs(date, 'D MMM');
                const today = dayjs();
                if (selectedDate.isSame(today, 'day')) {
                    return `Today : ${today.format('D MMM')}`;
                } else {
                    return `Date : ${selectedDate.format('D MMM')}`;
                }
        }
    }, [dateRange, selectedFilter]);

    const openSheet = (type: SheetType) => {
        const data = type === 'weekly' ? weekOptions : monthOptions;
        const selected = dateRange[type];

        showBottomSheet(() => (
            <FilterOptions
                type={type}
                data={data}
                selected={selected}
                handleDateRangeChange={handleDateRangeChange}
            />
        ));
    };

    const handleFilterSelection = (filter: FilterType) => {
        setSelectedFilter(filter);
        if (filter === 'weekly') {
            openSheet(filter);
        } else if (filter === 'monthly') {
            openSheet(filter);
        } else {
            setIsDatePickerVisible(true);
        }
    };

    return (
        <View className="flex-1 bg-background">
            <TitleHeader backArrow titleLeft={t('order_history')} shadowBottom borderBottom />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerClassName="flex-grow px-primary pt-primary gap-5"
                contentContainerStyle={{ paddingBottom: bottomInset }}>

                <FilterTab
                    selectedFilter={selectedFilter}
                    handleFilterSelection={handleFilterSelection}
                />

                <TouchableOpacity
                    className="flex-row items-center rounded-lg border border-border bg-white px-3 py-4 gap-4"
                    onPress={() => {
                        if (selectedFilter === 'daily') {
                            setIsDatePickerVisible(true);
                        } else if (selectedFilter === 'weekly') {
                            openSheet(selectedFilter);
                        } else if (selectedFilter === 'monthly') {
                            openSheet(selectedFilter);
                        }
                    }}
                    activeOpacity={0.9}>
                    <Feather name="calendar" size={20} color="black" />
                    <Text
                        className="flex-1 text-text16 text-secondary tracking-wide"
                        style={fonts.fontMedium}
                        numberOfLines={1}
                        ellipsizeMode="tail">
                        {getDisplayText}
                    </Text>
                    <Entypo
                        name="chevron-small-down"
                        size={24}
                        color="black"
                    />
                </TouchableOpacity>

                <DoubleOrders />
                <SingleOrder />
                <DoubleOrders />
                <SingleOrder />
            </ScrollView>

            <Calendar
                visible={isDatePickerVisible}
                setVisible={setIsDatePickerVisible}
                onConfirm={(date) => {
                    setDate(dayjs(date));
                    handleDateRangeChange(dayjs(date).format('D MMM'), 'daily');
                    setIsDatePickerVisible(false);
                }}
                date={date}
                setDate={setDate}
            />
        </View>
    );
};

export default OrderHistory;
