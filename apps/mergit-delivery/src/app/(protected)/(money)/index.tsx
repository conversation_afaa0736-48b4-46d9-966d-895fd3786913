import { ScrollView, View } from 'react-native';
import { TitleHeader } from 'components/common';
import { DepositCard, TransactionHistory } from 'components/money';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const Wallet = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1 bg-background">
            <TitleHeader backArrow title={t('wallets')} shadowBottom borderBottom />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerClassName="flex-grow px-primary pt-primary gap-8"
                contentContainerStyle={{ paddingBottom: bottomInset }}>
                <DepositCard />
                <TransactionHistory />
            </ScrollView>
        </View>
    );
};

export default Wallet;
