import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import { fonts } from '@mergit-mobile/styles';
import { Entypo } from '@expo/vector-icons';
import { router } from 'expo-router';
import { TitleHeader } from 'components/common';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const notifications = [
    {
        id: 1,
        titleKey: 'notification_pan_rejected_title',
        descriptionKey: 'notification_pan_rejected_description',
    },
    {
        id: 2,
        titleKey: 'notification_new_order_title',
        descriptionKey: 'notification_new_order_description',
    },
    {
        id: 3,
        titleKey: 'notification_new_order_title',
        descriptionKey: 'notification_new_order_description',
    },
];

const NotificationHome = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow title={t('notification')} shadowBottom />

            <FlatList
                data={notifications}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item: notification }) => (
                    <TouchableOpacity
                        key={notification.id}
                        testID={`notification-${notification.id}`}
                        activeOpacity={0.8}
                        onPress={() =>
                            router.push({
                                pathname: '/notification/detail',
                                params: {
                                    title: t(notification.titleKey),
                                    description: t(notification.descriptionKey),
                                },
                            })
                        }>
                        <View className="rounded-lg border border-border p-4 gap-2">
                            <View className="flex-row items-center">
                                <Text
                                    numberOfLines={1}
                                    ellipsizeMode="tail"
                                    style={fonts.fontSemiBold}
                                    className="flex-1 text-text16 text-secondary tracking-wider">
                                    {t(notification.titleKey)}
                                </Text>
                                <Entypo name="chevron-right" size={20} color="#717680" />
                            </View>
                            <Text
                                numberOfLines={1}
                                ellipsizeMode="tail"
                                style={fonts.fontRegular}
                                className="text-text15 tracking-wide text-secondary">
                                {t(notification.descriptionKey)}
                            </Text>
                        </View>
                    </TouchableOpacity>
                )}
                contentContainerClassName="flex-grow px-primary pt-primary gap-4"
                contentContainerStyle={{ paddingBottom: bottomInset }}
                showsVerticalScrollIndicator={false}
            />
        </View>
    );
};

export default NotificationHome;
