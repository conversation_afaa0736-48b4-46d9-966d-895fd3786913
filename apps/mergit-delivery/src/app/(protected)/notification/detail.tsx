import { View, Text } from 'react-native';
import { TitleHeader } from 'components/common';
import { useLocalSearchParams } from 'expo-router';
import { fonts } from '@mergit-mobile/styles';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native-gesture-handler';
import { useTabsStore } from '~/services/tabs';

const Detail = () => {
    const { title, description } = useLocalSearchParams();
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1 bg-white ">
            <TitleHeader backArrow title={t('notification')} shadowBottom />
            <ScrollView
                contentContainerClassName="flex-grow px-primary pt-primary"
                contentContainerStyle={{ paddingBottom: bottomInset }}
                showsVerticalScrollIndicator={false}>
                <View className="flex-1 gap-3">
                    <Text style={fonts.fontSemiBold} className="text-text17 text-secondary tracking-wider">
                        {title}
                    </Text>
                    <Text style={fonts.fontRegular} className=" text-text15 text-secondary tracking-wide">
                        {description}
                    </Text>
                </View>
            </ScrollView>
        </View>
    );
};

export default Detail;
