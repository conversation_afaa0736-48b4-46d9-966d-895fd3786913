import { Stack } from 'expo-router';
import { useEffect } from 'react';
import { useAuthStore } from 'services/auth';

export default function ProtectedLayout() {
    const { onboardingCompleted, setOnBoradingComplete } = useAuthStore();
    // useEffect(() => {
    //     setOnBoradingComplete(true);
    // }, []);

    return (
        <Stack
            screenOptions={{
                headerShown: false,
                animation: 'slide_from_right',
            }}>
            <Stack.Protected guard={!onboardingCompleted}>
                <Stack.Screen name="onboarding" />
            </Stack.Protected>

            <Stack.Protected guard={onboardingCompleted}>
                <Stack.Screen name="(tabs)" />
                <Stack.Screen name="(emergency-options)" />
                <Stack.Screen name="(incentive)" />
                <Stack.Screen name="(orders)" />
                <Stack.Screen name="(money)" />
                <Stack.Screen name="notification" />
                <Stack.Screen name="profile" />
            </Stack.Protected>
        </Stack>
    );
}
