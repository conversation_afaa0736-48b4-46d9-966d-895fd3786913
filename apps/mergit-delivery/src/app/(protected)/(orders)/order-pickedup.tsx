import { useEffect, useRef, useState } from 'react';
import { ScrollView, View, Text, BackHandler } from 'react-native';
import { router } from 'expo-router';
import { OrderStatus, useOrderStore } from 'services/orders';
import { CustomText, TitleHeader } from 'components/common';
import { fonts, shadow } from '@mergit-mobile/styles';
import { CustomerShopDetails, OrderDetails, SwipeSwitch } from 'components/orders';
import Animated, { FadeInUp, SlideInDown } from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const OrderPickedup = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const {
        currentRouteStop,
        updateCurrentRouteStop,
        orders,
        sellerOrdersIndex,
        updateOrderPackageImages,
        updateSellerOrdersIndex,
    } = useOrderStore();

    const [loading, setLoading] = useState(false);
    const ref = useRef<ScrollView>(null);

    const currentSellerOrder = orders?.routes[currentRouteStop]?.orders
        ? orders?.routes[currentRouteStop]?.orders[sellerOrdersIndex]
        : null;
    const currentUser = orders?.routes.find((route) => route.id === currentSellerOrder?.userId);

    const updateStatus = async () => {
        setLoading(true);
        try {
            if (currentSellerOrder) {
                updateOrderPackageImages({
                    subOrderId: currentSellerOrder.subOrderId,
                    status: OrderStatus.Picked_Up,
                });
                updateStates();
            }
        } catch (error) {
            console.log('Error update status in order pickedup : ', error);
        } finally {
            setLoading(false);
        }
    };

    const updateStates = async () => {
        try {
            const nextSellerOrderIndex = sellerOrdersIndex + 1;
            const nextRouteStop = currentRouteStop + 1;

            if (
                orders?.routes[currentRouteStop]?.orders &&
                nextSellerOrderIndex < orders?.routes[currentRouteStop]?.orders?.length
            ) {
                updateSellerOrdersIndex(nextSellerOrderIndex);
                ref.current?.scrollTo({ y: 0, animated: true });
            } else if (orders?.routes[nextRouteStop]?.userType === 'seller') {
                updateSellerOrdersIndex(-1);
                router.push('/order-pickup-location');
                updateCurrentRouteStop(nextRouteStop);
            } else {
                updateSellerOrdersIndex(-1);
                router.push('/order-drop-location');
                updateCurrentRouteStop(nextRouteStop);
            }
        } catch (error) {
            console.log('Error in update app states : ', error);
        }
    };

    useEffect(() => {
        const subscribe = BackHandler.addEventListener('hardwareBackPress', () => {
            router.dismissTo('/(protected)/(tabs)');
            return true;
        });
        return () => subscribe.remove();
    }, []);

    return (
        <View className="flex-1 bg-background">
            <TitleHeader
                backArrow
                titleLeft={t('reached_pickup_location')}
                nhIcon
                enhIcon
                shadowBottom
                onPressBack={() => router.dismissTo('/(protected)/(tabs)')}
            />

            <ScrollView
                ref={ref}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    flexGrow: 1,
                    paddingBottom: 20,
                    paddingTop: 35,
                }}>
                <View className="flex-1 gap-5 px-primary">
                    <Animated.View
                        entering={FadeInUp.duration(800).delay(200).springify()}
                        className="items-center gap-[6px]">
                        <CustomText
                            tKey="order_id"
                            className="text-text14 tracking-wide text-secondary"
                            style={fonts.fontRegular}
                        />

                        {orders?.routes[currentRouteStop]?.orders &&
                            orders?.routes[currentRouteStop]?.orders[sellerOrdersIndex] && (
                                <Text
                                    className="text-text17 tracking-wider text-primary"
                                    style={fonts.fontMedium}>
                                    #
                                    {
                                        orders?.routes[currentRouteStop]?.orders[sellerOrdersIndex]
                                            .subOrderId
                                    }
                                </Text>
                            )}
                    </Animated.View>

                    <Animated.View entering={FadeInUp.duration(800).delay(300).springify()}>
                        <OrderDetails currentUserOrder={currentSellerOrder} />
                    </Animated.View>

                    <Animated.View entering={FadeInUp.duration(800).delay(400).springify()}>
                        <CustomerShopDetails title="Customer Details" currentUser={currentUser} />
                    </Animated.View>

                    <Animated.View entering={FadeInUp.duration(800).delay(500).springify()}>
                        <CustomerShopDetails
                            title="Shop Details"
                            currentUser={orders?.routes[currentRouteStop]}
                        />
                    </Animated.View>
                </View>
            </ScrollView>

            <Animated.View
                entering={SlideInDown.duration(600).delay(300).damping(300)}
                className="rounded-t-2xl bg-white px-primary pt-7"
                style={[shadow, { paddingBottom: bottomInset }]}>
                <SwipeSwitch
                    title="Picked Order"
                    onToggle={updateStatus}
                    disabled={loading}
                    loading={loading}
                />
            </Animated.View>

        </View>
    );
};

export default OrderPickedup;
