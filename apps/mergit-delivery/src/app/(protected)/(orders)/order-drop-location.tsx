import { useEffect, useRef, useState } from 'react';
import { BackHandler, View } from 'react-native';
import { router } from 'expo-router';
import { useLocationStore } from '~/services/location';
import MapView, { Marker, Polyline, Region } from 'react-native-maps';
import { mapsApis } from 'services/maps';
import { useOrderStore } from 'services/orders';
import { colors } from 'utils/constants';
import { TitleHeader } from 'components/common';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator } from 'react-native';
import { StyleSheet } from 'react-native';
import PickupDropInfo from '~/components/orders/PickupDropInfo';

type CoordinatesType = {
    latitude: number;
    longitude: number;
};

const DropLocation = () => {
    const { t } = useTranslation();

    const { currentRouteStop, orders } = useOrderStore();
    const { deliveryPartnerLocation } = useLocationStore();
    const { fetchRoute } = mapsApis();

    const [travelLocation, setTravelLocation] = useState<CoordinatesType | undefined>(undefined);
    const [region, setRegion] = useState<Region | null>(null);
    const [coordinates, setCoordinates] = useState<CoordinatesType[] | undefined>(undefined);
    const [mapLoading, setMapLoading] = useState(true);

    const mapRef = useRef<MapView>(null);
    const customer = orders?.routes[currentRouteStop];

    useEffect(() => {
        if (
            deliveryPartnerLocation?.coords?.latitude !== undefined &&
            deliveryPartnerLocation?.coords?.longitude !== undefined &&
            travelLocation
        ) {
            const origin = deliveryPartnerLocation.coords;
            const destination = travelLocation;

            const latitudeDelta = Math.abs(origin.latitude - destination.latitude) * 2;
            const longitudeDelta = Math.abs(origin.longitude - destination.longitude) * 2;

            fetchCoordinates();

            setRegion({
                latitude: (origin.latitude + destination.latitude) / 2,
                longitude: (origin.longitude + destination.longitude) / 2,
                latitudeDelta,
                longitudeDelta,
            });
        }
    }, [deliveryPartnerLocation?.coords, travelLocation]);

    const fetchCoordinates = async () => {
        if (deliveryPartnerLocation?.coords && travelLocation) {
            const polyline = await fetchRoute({
                deliveryPartnerLocation: deliveryPartnerLocation.coords,
                pickupLocation: travelLocation,
            });
            setCoordinates(polyline);
        }
    };

    useEffect(() => {
        if (customer?.location) {
            setTravelLocation({
                latitude: customer?.location.lat || 0,
                longitude: customer?.location.lng || 0,
            });
        }
    }, [currentRouteStop, customer?.location]);

    useEffect(() => {
        const subscribe = BackHandler.addEventListener('hardwareBackPress', () => {
            router.dismissTo('/(protected)/(tabs)');
            return true;
        });
        return () => subscribe.remove();
    }, []);

    return (
        <View className="flex-1 bg-background">
            <TitleHeader
                backArrow
                titleLeft={t('reach_drop_location')}
                nhIcon
                enhIcon
                shadowBottom
                onPressBack={() => router.dismissTo('/(protected)/(tabs)')}
            />

            <View className="flex-1 bg-background">
                {mapLoading && (
                    <View
                        className="flex-1 items-center justify-center"
                        style={StyleSheet.absoluteFillObject}>
                        <ActivityIndicator size="large" color={colors.primary} />
                    </View>
                )}
                {region && travelLocation && coordinates && (
                    <MapView
                        ref={mapRef}
                        style={{ flex: 1 }}
                        region={region}
                        provider="google"
                        mapType="standard"
                        showsBuildings
                        showsUserLocation
                        onMapReady={() => setMapLoading(false)}
                        onLayout={() => setMapLoading(false)}
                        zoomEnabled={true}>
                        <Marker
                            coordinate={{
                                latitude: customer?.location.lat || 0,
                                longitude: customer?.location.lng || 0,
                            }}
                            title={customer?.name}
                        />
                        <Polyline
                            coordinates={coordinates}
                            strokeColor={colors.primary}
                            strokeWidth={5}
                        />
                    </MapView>
                )}
            </View>

            <PickupDropInfo
                travelLocation={travelLocation}
                userType="customer"
                isButtonEnabled={false}
            />

        </View>
    );
};

export default DropLocation;
