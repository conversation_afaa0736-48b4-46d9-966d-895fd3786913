import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { router } from 'expo-router';
import { OrderStatus, useOrderStore } from 'services/orders';
import { CustomText, TitleHeader } from 'components/common';
import Animated, { FadeInUp, SlideInDown } from 'react-native-reanimated';
import { CustomerShopDetails, OrderDetails, SwipeSwitch } from 'components/orders';
import { fonts, shadow } from '@mergit-mobile/styles';
import { Feather } from '@expo/vector-icons';
import { colors } from 'utils/constants';
import { Image } from 'expo-image';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const OrderDeliver = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const {
        userOrdersIndex,
        orders,
        currentRouteStop,
        orderPackageImages,
        updateOrderPackageImages,
    } = useOrderStore();

    const [loading, setLoading] = useState(false);

    const currentUserOrder = orders?.routes[currentRouteStop]?.orders
        ? orders?.routes[currentRouteStop]?.orders[userOrdersIndex]
        : null;
    const currentUserOrderPackagePhoto = orderPackageImages?.find(
        (orderImage) => orderImage.subOrderId === currentUserOrder?.subOrderId
    );

    const updateStatus = async () => {
        setLoading(true);
        try {
            if (
                orders?.routes[currentRouteStop]?.orders &&
                orders?.routes[currentRouteStop]?.orders?.length > userOrdersIndex
            ) {
                if (currentUserOrder) {
                    updateOrderPackageImages({
                        subOrderId: currentUserOrder.subOrderId,
                        status: OrderStatus.Completed,
                    });
                    router.push('/order-completed');
                }
            }
        } catch (error) {
            console.log('Error update status in order deliver : ', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const subscribe = BackHandler.addEventListener('hardwareBackPress', () => {
            router.dismissTo('/(protected)/(tabs)');
            return true;
        });
        return () => subscribe.remove();
    }, []);

    return (
        <View className="flex-1 bg-background">
            <TitleHeader
                backArrow
                titleLeft={t('deliver_order')}
                nhIcon
                enhIcon
                shadowBottom
                onPressBack={() => router.dismissTo('/(protected)/(tabs)')}
            />

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    flexGrow: 1,
                    paddingBottom: 20,
                    paddingTop: 35,
                }}>
                <View className="flex-1 gap-5 px-primary">
                    <Animated.View
                        entering={FadeInUp.duration(800).delay(200).springify()}
                        className="items-center gap-[6px]">
                        <CustomText
                            tKey="order_id"
                            className="text-text14 tracking-wide text-secondary"
                            style={fonts.fontRegular}
                        />

                        <Text
                            className="text-text17 tracking-wider text-primary"
                            style={fonts.fontMedium}>
                            #{currentUserOrder?.subOrderId}
                        </Text>
                    </Animated.View>

                    <Animated.View entering={FadeInUp.duration(800).delay(300).springify()}>
                        <OrderDetails currentUserOrder={currentUserOrder} isPaid />
                    </Animated.View>

                    <Animated.View entering={FadeInUp.duration(800).delay(400).springify()}>
                        <CustomerShopDetails
                            title="Customer Details"
                            currentUser={orders?.routes[currentRouteStop]}
                        />
                    </Animated.View>

                    <Animated.View
                        entering={FadeInUp.duration(800).delay(500).springify()}
                        className="gap-5">
                        <CustomText
                            tKey="upload_package_photo"
                            className="text-text16 tracking-wider text-secondary"
                            style={fonts.fontSemiBold}
                        />

                        <View className="flex-1 flex-row items-center gap-4">
                            <View className="h-[110px] flex-1 rounded-lg border border-border bg-white">
                                {!currentUserOrderPackagePhoto?.imageUrl ? (
                                    <TouchableOpacity
                                        activeOpacity={0.8}
                                        className="h-full items-center justify-center gap-4 px-4"
                                        style={shadow}
                                        onPress={() => router.push('/order-package-photo')}>
                                        <Feather name="upload" size={24} color={colors.primary} />
                                        <CustomText
                                            tKey="package_photo"
                                            className="text-text14 tracking-wider text-secondary"
                                            style={fonts.fontMedium}
                                        />
                                    </TouchableOpacity>
                                ) : (
                                    <Image
                                        source={{
                                            uri: currentUserOrderPackagePhoto.imageUrl,
                                        }}
                                        style={{
                                            height: '100%',
                                            width: '100%',
                                        }}
                                        contentFit="contain"
                                    />
                                )}
                            </View>
                            <View className="flex-1" />
                        </View>
                    </Animated.View>
                </View>
            </ScrollView>

            <Animated.View
                entering={SlideInDown.duration(700).delay(300).damping(300)}
                className="rounded-t-2xl bg-white px-primary pt-7"
                style={[shadow, { paddingBottom: bottomInset }]}>
                <SwipeSwitch
                    title="Order Delivered"
                    onToggle={updateStatus}
                    disabled={loading}
                    loading={loading}
                />
            </Animated.View>

        </View>
    );
};

export default OrderDeliver;
