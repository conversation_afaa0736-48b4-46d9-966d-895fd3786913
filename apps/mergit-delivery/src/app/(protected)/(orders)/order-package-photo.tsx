import { View } from 'react-native';
import { TitleHeader } from 'components/common';
import { DocCamera } from '~/components/onboarding';
import { router } from 'expo-router';
import { useOrderStore } from 'services/orders';
import { useTranslation } from 'react-i18next';

const OrderPackagePhoto = () => {
    const { t } = useTranslation();
    const { updateOrderPackageImages, orders, currentRouteStop, userOrdersIndex } = useOrderStore();

    const captureImage = (uri: string) => {
        if (orders?.routes[currentRouteStop]?.orders) {
            updateOrderPackageImages({
                imageUrl: uri,
                subOrderId: orders?.routes[currentRouteStop]?.orders[userOrdersIndex].subOrderId,
            });
        }
    };

    const onClose = () => {
        router.back();
    };

    return (
        <View className="flex-1 bg-background">
            <TitleHeader backArrow title={t('package_photo')} shadowBottom />
            <DocCamera
                type="package"
                forDoc="packagePhoto"
                view="back"
                cardName="Package Photo"
                onCapture={captureImage}
                onClose={onClose}
            />
        </View>
    );
};

export default OrderPackagePhoto;
