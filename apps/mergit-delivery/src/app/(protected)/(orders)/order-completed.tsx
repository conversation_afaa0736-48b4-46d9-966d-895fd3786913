import { Text, View } from 'react-native';
import { router } from 'expo-router';
import { OrderStatus, useOrderStore } from 'services/orders';
import { SuccessFailure } from '@mergit-mobile/components';
import { fonts, shadow } from '@mergit-mobile/styles';
import { rupeeSymbol } from 'utils/constants';
import { SwipeSwitch } from 'components/orders';
import { CustomText } from 'components/common';
import { useTabsStore } from '~/services/tabs';

const OrderCompleted = () => {
    const {
        userOrdersIndex,
        orders,
        currentRouteStop,
        updateCurrentRouteStop,
        updatePreviousOrders,
        updateUserOrdersIndex,
        updateOrders,
        updateOrderPackageImages,
        updateSellerOrdersIndex,
    } = useOrderStore();
    const { bottomInset } = useTabsStore();

    const custOrder =
        orders?.routes[currentRouteStop]?.orders &&
        orders?.routes[currentRouteStop]?.orders[userOrdersIndex];

    const updateStatus = async () => {
        try {
            if (
                orders?.routes[currentRouteStop]?.orders &&
                orders?.routes[currentRouteStop]?.orders?.length > userOrdersIndex + 1
            ) {
                const subOrderIds =
                    orders?.routes[currentRouteStop].orders[userOrdersIndex].subOrderId;
                if (subOrderIds) {
                    updateOrderPackageImages({
                        subOrderId: subOrderIds,
                        status: OrderStatus.Completed,
                    });
                }
                updateUserOrdersIndex(userOrdersIndex + 1);
                router.push('/order-deliver');
            } else if (orders?.routes && orders?.routes?.length > currentRouteStop + 1) {
                updateCurrentRouteStop(currentRouteStop + 1);
                updateUserOrdersIndex(-1);
                router.push('/order-drop-location');
            } else {
                updateUserOrdersIndex(-1);
                updateCurrentRouteStop(-1);
                updateOrders(null);
                updatePreviousOrders(false);
                updateSellerOrdersIndex(-1);
                updateOrderPackageImages(null);
                router.dismissTo('/(protected)/(tabs)');
            }
        } catch (error) {
            console.log('Error update status in order deliver : ', error);
        } finally {
        }
    };

    return (
        <View className="flex-1 bg-background">
            <View className="flex-1 items-center justify-center gap-6 px-primary">
                <SuccessFailure
                    status="success"
                    outerWidth={130}
                    outerHeight={130}
                    innerHeight={80}
                    innerWidth={80}
                    iconSize={30}
                />
                <View className="items-center justify-center gap-3">
                    <Text
                        className="text-text15 tracking-wide text-primary"
                        style={fonts.fontSemiBold}>
                        #{custOrder?.subOrderId}
                    </Text>
                    <CustomText
                        tKey="order_completed"
                        className="text-text18 tracking-wide text-secondary"
                        style={fonts.fontBold}
                    />

                    <CustomText
                        tKey="order_completed_success"
                        className="text-text15 tracking-wide text-neutral"
                        style={fonts.fontRegular}
                    />
                </View>
                <View
                    className="w-full gap-7 rounded-lg border border-border bg-white p-4"
                    style={shadow}>
                    <View className="gap-1">
                        <CustomText
                            tKey="order_earning"
                            className="text-text15 tracking-wide text-neutral"
                            style={fonts.fontRegular}
                        />

                        <Text
                            className="text-text15 tracking-wide text-secondary"
                            style={fonts.fontSemiBold}>
                            {rupeeSymbol}30
                        </Text>
                    </View>
                    <View className="gap-1">
                        <CustomText
                            tKey="order_total_distance"
                            className="text-text15 tracking-wide text-neutral"
                            style={fonts.fontRegular}
                        />

                        <Text
                            className="text-text15 tracking-wide text-secondary"
                            style={fonts.fontSemiBold}>
                            4 km
                        </Text>
                    </View>
                    <View className="gap-1">
                        <CustomText
                            tKey="time"
                            className="text-text15 tracking-wide text-neutral"
                            style={fonts.fontRegular}
                        />
                        <Text
                            className="text-text15 tracking-wide text-secondary"
                            style={fonts.fontSemiBold}>
                            30 mins
                        </Text>
                    </View>
                </View>
            </View>

            <View className="px-primary" style={{ paddingBottom: bottomInset }}>
                <SwipeSwitch title="Continue" onToggle={updateStatus} />
            </View>

        </View>
    );
};

export default OrderCompleted;
