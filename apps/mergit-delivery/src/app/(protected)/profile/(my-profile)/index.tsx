import { View } from 'react-native';
import { BankDetails, PersonalDetails, RiderDetails } from 'components/profile';
import { router } from 'expo-router';
import { TitleHeader } from 'components/common';
import { ScrollView } from 'react-native-gesture-handler';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const MyProfile = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1">
            <View className="">
                <TitleHeader
                    title={t('my_profile')}
                    backArrow
                    shadowBottom
                    onPressBack={router.back}
                />
            </View>

            <ScrollView
                contentContainerStyle={{ flexGrow: 1, paddingBottom: bottomInset }}
                showsVerticalScrollIndicator={false}>
                <View className="px-primary pt-primary gap-4">
                    <RiderDetails />

                    <PersonalDetails />

                    <BankDetails />
                </View>
            </ScrollView>
        </View>
    );
};

export default MyProfile;
