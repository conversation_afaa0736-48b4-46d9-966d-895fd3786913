import { View, ScrollView, Platform, Keyboard } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { CustomText, TitleHeader } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { CustomBtn, InputField } from '@mergit-mobile/components';
import { router } from 'expo-router';
import { useCustomModal } from '@mergit-mobile/components';
import { bankDetailsSchema, useProfileStore } from 'services/profile';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const EditBankDetails = () => {
    const { fullName, accountNumber, ifscCode, setBankDetails } = useProfileStore();
    const { t } = useTranslation();

    const { showModal, hideModal } = useCustomModal();
    const { bottomInset } = useTabsStore();

    const {
        control,
        handleSubmit,
        formState: { errors },
        getValues,
    } = useForm({
        defaultValues: {
            fullName,
            accountNumber,
            reAccountNumber: accountNumber,
            ifscCode,
        },
        resolver: yupResolver(bankDetailsSchema),
    });

    const confirmUpdate = () => {
        const { fullName, accountNumber, ifscCode } = getValues();
        setBankDetails({
            fullName,
            accountNumber,
            ifscCode,
        });
        hideModal();
        router.back();
    };

    const showEditModel = () => {
        Keyboard.dismiss();
        showModal(
            <>
                <CustomText
                    tKey="update_bank_details"
                    className="mb-4 text-text17 tracking-wider text-secondary"
                    style={fonts.fontSemiBold}
                />

                <CustomText
                    tKey="update_bank_confirmation"
                    className="mb-6 text-text15 tracking-wide text-secondary"
                />

                <View className="flex-row justify-between gap-4">
                    <CustomBtn
                        title={t('cancel')}
                        onPress={hideModal}
                        backgroundColor="#fff"
                        borderColor="#1F7575"
                        textStyle={{
                            color: '#1F7575',
                        }}
                        btnStyle={{ flex: 1 }}
                    />

                    <CustomBtn
                        title={t('confirm')}
                        onPress={confirmUpdate}
                        btnStyle={{ flex: 1 }}
                    />
                </View>
            </>
        );
    };

    return (
        <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
            <View className="flex-1 bg-white">
                <TitleHeader
                    title={t('edit_bank_details')}
                    backArrow
                    shadowBottom
                    onPressBack={() => router.back()}
                />

                <ScrollView
                    contentContainerClassName='flex-grow p-primary gap-8'
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled">
                    <CustomText
                        tKey="provide_bank_details"
                        className="mt-5 text-xl tracking-wider"
                        style={fonts.fontSemiBold}
                    />

                    <View>
                        <Controller
                            control={control}
                            name="fullName"
                            render={({ field: { onChange, value } }) => (
                                <InputField
                                    label="Full Name"
                                    testID="full-name"
                                    value={value}
                                    onChangeText={onChange}
                                    placeholder="Enter your full name"
                                    errorMsg={errors.fullName?.message}
                                    inputMode="text"
                                    autoCapitalize="words"
                                    maxLength={50}
                                />
                            )}
                        />
                    </View>

                    <View>
                        <Controller
                            control={control}
                            name="accountNumber"
                            render={({ field: { onChange, value } }) => (
                                <InputField
                                    label="Account Number"
                                    value={value}
                                    testID="account-number"
                                    onChangeText={onChange}
                                    placeholder="Enter account number"
                                    keyboardType="numeric"
                                    inputMode="numeric"
                                    errorMsg={errors.accountNumber?.message}
                                    maxLength={18}
                                />
                            )}
                        />
                    </View>

                    <View>
                        <Controller
                            control={control}
                            name="reAccountNumber"
                            render={({ field: { onChange, value } }) => (
                                <InputField
                                    label="Re-Account Number"
                                    value={value}
                                    onChangeText={onChange}
                                    testID="re-account-number"
                                    placeholder="Re-enter account number"
                                    keyboardType="numeric"
                                    inputMode="numeric"
                                    errorMsg={errors.reAccountNumber?.message}
                                    maxLength={18}
                                />
                            )}
                        />
                    </View>

                    <View>
                        <Controller
                            control={control}
                            name="ifscCode"
                            render={({ field: { onChange, value } }) => (
                                <InputField
                                    label="IFSC Code"
                                    value={value}
                                    onChangeText={onChange}
                                    testID="ifsc-code"
                                    placeholder="Enter IFSC code"
                                    inputMode="text"
                                    autoCapitalize="characters"
                                    errorMsg={errors.ifscCode?.message}
                                    maxLength={11}
                                />
                            )}
                        />
                    </View>
                </ScrollView>

                <View className='px-primary pt-primary' style={{ paddingBottom: bottomInset }}>
                    <CustomBtn
                        title={t('update_bank_details')}
                        onPress={handleSubmit(showEditModel)}
                    />
                </View>

            </View>
        </KeyboardAvoidingView>
    );
};

export default EditBankDetails;
