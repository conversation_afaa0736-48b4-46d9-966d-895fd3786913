import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    FlatList,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import * as Contacts from 'expo-contacts';
import { fonts } from '@mergit-mobile/styles';
import { CustomText, TitleHeader } from 'components/common';
import { router } from 'expo-router';
import { useProfileStore } from 'services/profile';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

type ContactItem = {
    name: string;
    phone: string;
};

const ReferralsList: React.FC = () => {
    const [contacts, setContacts] = useState<ContactItem[]>([]);
    const [search, setSearch] = useState<string>('');

    const { setSelectedContact } = useProfileStore();
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    useEffect(() => {
        (async () => {
            const { status } = await Contacts.requestPermissionsAsync();
            if (status === 'granted') {
                const { data } = await Contacts.getContactsAsync({
                    fields: [Contacts.Fields.PhoneNumbers],
                });

                if (data.length > 0) {
                    const formattedContacts: ContactItem[] = data
                        .filter(
                            (contact) => contact.phoneNumbers && contact.phoneNumbers.length > 0
                        )
                        .map((contact) => ({
                            name: contact.name ?? 'Unknown',
                            phone: contact.phoneNumbers?.[0]?.number ?? '',
                        }));

                    setContacts(formattedContacts);
                }
            }
        })();
    }, []);

    const filteredContacts = contacts.filter(
        (c) => c.name.toLowerCase().includes(search.toLowerCase()) || c.phone.includes(search)
    );

    const handleContactSelect = (contact: ContactItem) => {
        setSelectedContact({
            name: contact.name,
            number: contact.phone,
            city: '',
        });
        router.back();
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow title={t('your_contact')} />

            <View className="gap-5 px-primary">
                <View className="flex-row items-center gap-4 rounded-lg border border-border p-4">
                    <Feather name="search" size={20} color="#999" />
                    <TextInput
                        placeholder="Search by Name or Phone"
                        className="w-full"
                        placeholderTextColor="#717680"
                        value={search}
                        onChangeText={setSearch}
                        style={{
                            fontFamily: 'DMSans-Medium',
                            fontSize: 15,
                            letterSpacing: 0.3,
                        }}
                    />
                </View>
                <View className="h-px w-full bg-border" />
            </View>

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}>
                <FlatList
                    data={filteredContacts}
                    keyExtractor={(_, index) => index.toString()}
                    contentContainerClassName="flex-grow px-primary pt-primary"
                    contentContainerStyle={{ paddingBottom: bottomInset }}
                    showsVerticalScrollIndicator={false}
                    ListEmptyComponent={
                        <CustomText
                            tKey="no_contacts_found"
                            style={fonts.fontMedium}
                            className="text-center text-text14 tracking-wide text-neutral"
                        />
                    }
                    renderItem={({ item }) => (
                        <View className="flex-row items-center gap-4">
                            <View className="flex-1 gap-1">
                                <Text
                                    style={fonts.fontMedium}
                                    className="text-text15 tracking-wide text-secondary">
                                    {item.name}
                                </Text>
                                <Text
                                    style={fonts.fontRegular}
                                    className="text-text14 text-grayText">
                                    {item.phone}
                                </Text>
                            </View>

                            <TouchableOpacity
                                activeOpacity={0.8}
                                onPress={() => handleContactSelect(item)}>
                                <CustomText
                                    tKey="select"
                                    style={fonts.fontSemiBold}
                                    className="text-text15 tracking-wide text-primary underline"
                                />
                            </TouchableOpacity>
                        </View>
                    )}
                    ItemSeparatorComponent={() => <View className="my-4 h-px w-full bg-border" />}
                />
            </KeyboardAvoidingView>
        </View>
    );
};

export default ReferralsList;
