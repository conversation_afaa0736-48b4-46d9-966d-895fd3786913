import { View, ScrollView } from 'react-native';
import { router } from 'expo-router';
import { GradientCard, ReferralCard, ReferralWorkCard } from 'components/profile/referrals';
import { useTranslation } from 'react-i18next';
import { CustomBtn } from '@mergit-mobile/components';
import { TitleHeader } from 'components/common';
import { useTabsStore } from '~/services/tabs';

const ReferralHome = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1 bg-background">
            <TitleHeader backArrow titleLeft={t('referrals')} shadowBottom />
            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ flexGrow: 1, paddingBottom: 20, gap: 20 }}>
                <GradientCard />
                <View className="flex-1 gap-5 px-primary">
                    <ReferralCard />
                    <ReferralWorkCard />
                </View>
            </ScrollView>

            <View className="border-t border-border bg-white px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
                <CustomBtn
                    title={t('refer_&_earnmore')}
                    onPress={() => router.push('/profile/referral-form')}
                    height={48}
                />
            </View>
        </View>
    );
};

export default ReferralHome;
