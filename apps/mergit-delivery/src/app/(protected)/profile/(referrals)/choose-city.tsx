import { Feather } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { fonts } from '@mergit-mobile/styles';
import { fetchCities, useCityStore } from 'services/city';
import { CustomText } from 'components/common';
import { useProfileStore } from 'services/profile';
import { useTabsStore } from '~/services/tabs';

const ChooseCity = () => {
    const { cities, selectedCity, setSelectedCity, setCities } = useCityStore();
    const { selectedContact, setSelectedContact } = useProfileStore();
    const { bottomInset } = useTabsStore();

    const [search, setSearch] = useState('');

    useEffect(() => {
        const loadCities = async () => {
            const fetched = await fetchCities('India', 'Tamil Nadu');
            setCities(fetched);
        };
        loadCities();
    }, []);

    const filteredCities = cities.filter((c) =>
        c.name.toLowerCase().includes(search.toLowerCase())
    );

    const handleCitySelect = (city: any) => {
        setSelectedCity(city);
        setSelectedContact({
            ...selectedContact,
            city: city.name,
            name: selectedContact?.name || '',
            number: selectedContact?.number || '',
        });
        router.back();
    };

    return (
        <SafeAreaView className="flex-1 bg-white" edges={['top']}>
            <View className="flex-row items-center justify-between gap-20 border-b border-border p-primary">
                <Feather
                    name="chevron-left"
                    size={25}
                    color="black"
                    onPress={() => router.back()}
                />
                <CustomText
                    tKey="select_city"
                    style={fonts.fontSemiBold}
                    className="text-text16 text-secondary"
                />

                <View className="w-6" />
            </View>

            <View className="mt-5 h-[100%] flex-1 gap-4 px-primary">
                <View className="flex-row items-center gap-4 rounded-lg border border-border px-4">
                    <Feather name="search" size={20} color="#717680" />
                    <TextInput
                        placeholder="Search City"
                        value={search}
                        onChangeText={setSearch}
                        placeholderTextColor="#717680"
                        className="w-full py-4 text-text16 text-secondary"
                    />
                </View>

                <FlatList
                    data={filteredCities}
                    keyExtractor={(item) => item.id}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: bottomInset }}
                    renderItem={({ item }) => (
                        <TouchableOpacity
                            className="flex-row items-center justify-between border-b border-border py-6"
                            onPress={() => handleCitySelect(item)}
                            activeOpacity={0.9}>
                            <Text className="text-text16 text-secondary">{item.name}</Text>

                            <View
                                className={`h-6 w-6 items-center justify-center rounded-full border-2 ${
                                    selectedCity?.id === item?.id
                                        ? 'border-primary'
                                        : 'border-neutral'
                                }`}>
                                {selectedCity?.id === item?.id && (
                                    <View
                                        style={{
                                            height: 11,
                                            width: 11,
                                            borderRadius: 6,
                                            backgroundColor: '#1F7575',
                                        }}
                                    />
                                )}
                            </View>
                        </TouchableOpacity>
                    )}
                />
            </View>
        </SafeAreaView>
    );
};

export default ChooseCity;
