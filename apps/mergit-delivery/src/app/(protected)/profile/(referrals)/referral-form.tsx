import { useEffect } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    TextInput,
    Platform,
    ScrollView,
    TouchableWithoutFeedback,
    Keyboard,
} from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { fonts } from '@mergit-mobile/styles';
import { InputField, CustomBtn } from '@mergit-mobile/components';
import ContactIcon from 'assets/icons/proflie/Contact';
import { router } from 'expo-router';
import { CustomText, TitleHeader } from 'components/common';
import { useCityStore } from 'services/city';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useTranslation } from 'react-i18next';
import { referralSchema } from 'services/profile';
import { useTabsStore } from '~/services/tabs';

const ReferralForm = () => {
    const { selectedCity, setSelectedCity } = useCityStore();
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const {
        control,
        handleSubmit,
        formState: { errors, isValid },
        setValue,
    } = useForm({
        resolver: yupResolver(referralSchema),
        mode: 'onChange',
        defaultValues: {
            contactNumber: '',
            contactName: '',
            cityName: '',
        },
    });

    useEffect(() => {
        if (selectedCity?.name) {
            setValue('cityName', selectedCity.name, { shouldValidate: true });
        }
    }, [selectedCity]);

    const handleContactSelect = () => {
        router.push('/profile/referrals-list');
    };

    const handleCitySelect = () => {
        router.push('/profile/choose-city');
    };

    const onSubmit = (data: any) => {
        console.log('Form Data:', data);
        setSelectedCity(null);
        router.back();
    };

    return (
        <KeyboardAvoidingView
            className="flex-1 bg-white"
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <View className="flex-1">
                    <TitleHeader shadowBottom backArrow title={t('add_referral_details')} />

                    <ScrollView
                        keyboardShouldPersistTaps="handled"
                        contentContainerStyle={{ flexGrow: 1 }}>
                        <View className="mb-4 mt-5 px-primary">
                            <Controller
                                control={control}
                                name="contactNumber"
                                render={({ field }) => (
                                    <InputField
                                        label="Contact Number"
                                        placeholder="Enter Contact Number"
                                        value={field.value}
                                        onChangeText={field.onChange}
                                        errorMsg={errors.contactNumber?.message}
                                        required
                                        iconRight={
                                            <TouchableOpacity
                                                onPress={handleContactSelect}
                                                activeOpacity={0.9}>
                                                <ContactIcon />
                                            </TouchableOpacity>
                                        }
                                        inputMode="text"
                                    />
                                )}
                            />
                        </View>

                        <View className="mb-4 px-primary">
                            <Controller
                                control={control}
                                name="contactName"
                                render={({ field }) => (
                                    <InputField
                                        label="Contact Name"
                                        placeholder="Enter Contact Name"
                                        value={field.value}
                                        onChangeText={field.onChange}
                                        errorMsg={errors.contactName?.message}
                                        required
                                        inputMode="numeric"
                                    />
                                )}
                            />
                        </View>

                        <View className="mb-6 px-primary">
                            <Controller
                                control={control}
                                name="cityName"
                                render={({ field }) => (
                                    <View style={{ position: 'relative' }}>
                                        <TextInput
                                            editable={false}
                                            placeholder="Friend's City Name"
                                            placeholderTextColor="#414651"
                                            style={{
                                                borderWidth: 1,
                                                paddingHorizontal: 15,
                                                borderColor: errors.cityName ? 'red' : '#999',
                                                paddingVertical: 18,
                                                fontSize: 16,
                                                borderRadius: 8,
                                                paddingRight: 100,
                                                fontWeight: '400',
                                                height: 56,
                                            }}
                                            value={field.value}
                                        />

                                        <TouchableOpacity
                                            activeOpacity={0.8}
                                            onPress={handleCitySelect}
                                            style={{
                                                position: 'absolute',
                                                right: 20,
                                                top: 0,
                                                bottom: 0,
                                                justifyContent: 'center',
                                            }}>
                                            <CustomText
                                                tKey="selectCity"
                                                style={[
                                                    {
                                                        fontSize: 16,
                                                        textDecorationLine: 'underline',
                                                    },
                                                    fonts.fontBold,
                                                ]}
                                                className="text-primary"
                                            />
                                        </TouchableOpacity>

                                        {errors.cityName?.message && (
                                            <Text
                                                style={{
                                                    color: '#F04438',
                                                    fontSize: 14,
                                                    marginTop: 3,
                                                    fontFamily: 'DMSans-Medium',
                                                    letterSpacing: 0.2,
                                                }}>
                                                *{errors.cityName.message}
                                            </Text>
                                        )}
                                    </View>
                                )}
                            />
                        </View>
                    </ScrollView>

                    <View className="px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
                        <CustomBtn
                            title={t('invite')}
                            onPress={handleSubmit(onSubmit)}
                            disabled={!isValid}
                        />
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
    );
};

export default ReferralForm;
