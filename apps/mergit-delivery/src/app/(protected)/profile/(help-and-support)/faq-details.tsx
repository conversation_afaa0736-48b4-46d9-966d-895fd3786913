import { View, Text, ScrollView } from 'react-native';
import { CustomText, TitleHeader } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { CustomBtn, useCustomModal, SuccessFailure } from '@mergit-mobile/components';
import { useTranslation } from 'react-i18next';
import { faqList } from '~/utils/mock';
import { useMemo } from 'react';
import { useTabsStore } from '~/services/tabs';

const FAQDetail = () => {
    const { id } = useLocalSearchParams();

    const faq = useMemo(() => {
        return faqList.find((item) => item.id === id);
    }, [id]);

    const { showModal, hideModal } = useCustomModal();
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    const handleRequestCall = () => {
        handleOpenModal();
    };

    const handleCloseModal = () => {
        hideModal();
        router.back();
    };

    const handleOpenModal = () => {
        showModal(
            <View className="w-full items-center gap-4">
                <View className="flex-row items-center justify-center bg-white">
                    <SuccessFailure
                        outerWidth={130}
                        outerHeight={130}
                        innerHeight={80}
                        innerWidth={80}
                        iconSize={30}
                        status="success"
                    />
                </View>
                <CustomText
                    tKey="thankYou"
                    style={fonts.fontBold}
                    className="text-text18 text-secondary"
                />
                <CustomText
                    tKey="teamWillContact"
                    style={fonts.fontMedium}
                    className="text-center text-text15 text-secondary"
                />
                <CustomBtn
                    onPress={handleCloseModal}
                    title={t('okay')}
                    height={45}
                />
            </View>
        );
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow title={t('get_help')} shadowBottom />

            <ScrollView contentContainerClassName='flex-grow' showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: bottomInset }}>
                <View className="flex-1 gap-5 p-primary">
                    <Text
                        style={fonts.fontSemiBold}
                        className="text-text16 tracking-wide text-secondary">
                        {t(faq?.title || '')}
                    </Text>
                    <Text style={fonts.fontRegular} className="text-text15 text-secondary">
                        {t(faq?.description || '')}
                    </Text>
                    <View className="h-px w-full bg-border" />
                    <View className="gap-5">
                        <CustomText
                            tKey="stillNeedSupport"
                            style={fonts.fontSemiBold}
                            className="test-text16 tracking-wide text-secondary"
                        />

                        <View className="items-start">
                            <CustomBtn
                                onPress={handleRequestCall}
                                title={t('request_callback')}
                                icon={<Ionicons name="call-outline" size={18} color="#fff" />}
                                height={45}
                            />
                        </View>
                    </View>
                </View>
            </ScrollView>

        </View>
    );
};

export default FAQDetail;
