import { View, Pressable, FlatList } from 'react-native';
import { TitleHeader, CustomText } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { FontAwesome6 } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { faqList } from '~/utils/mock';
import { useTabsStore } from '~/services/tabs';

const FAQ = () => {
    const { t } = useTranslation();
    const { bottomInset } = useTabsStore();

    return (
        <View className="flex-1 gap-4 bg-white">
            <TitleHeader backArrow title={t('faqs')} shadowBottom />

            <View className="flex-1 bg-white px-primary">
                <CustomText
                    tKey="needHelpWithOrder"
                    style={fonts.fontSemiBold}
                    className="text-text16 tracking-wide text-secondary"
                />

                <FlatList
                    data={faqList}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                        <Pressable
                            onPress={() =>
                                router.push({
                                    pathname: '/profile/faq-details',
                                    params: {
                                        id: item.id
                                    },
                                })
                            }
                            className="flex-row items-center justify-center py-5">
                            <CustomText
                                tKey={item.title}
                                style={fonts.fontRegular}
                                className="flex-1 text-text15 tracking-wide text-secondary"
                            />
                            <FontAwesome6 name="angle-right" size={17} color="#1F7575" />
                        </Pressable>
                    )}
                    ItemSeparatorComponent={() => <View className="h-px w-full bg-border" />}
                    showsVerticalScrollIndicator={false}
                    contentContainerClassName="flex-grow"
                    contentContainerStyle={{ paddingBottom: bottomInset }}
                />
            </View>
        </View>
    );
};

export default FAQ;
