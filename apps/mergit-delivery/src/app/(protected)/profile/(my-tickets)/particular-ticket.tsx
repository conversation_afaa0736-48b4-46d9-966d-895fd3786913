import { View, Text, ScrollView } from 'react-native';
import React, { useState } from 'react';
import { CustomText, Modal, TitleHeader } from 'components/common';
import { fonts } from '@mergit-mobile/styles';
import { router, useLocalSearchParams } from 'expo-router';
import { CustomBtn, SuccessFailure, useBottomSheet } from '@mergit-mobile/components';
import { Badge } from 'components/incentives';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTabsStore } from '~/services/tabs';

const ParticularTicket = () => {
    const { id, title, description } = useLocalSearchParams();
    const { t } = useTranslation();

    const { bottomInset } = useTabsStore();
    const { showBottomSheet, hideBottomSheet } = useBottomSheet();

    const openBottomSheet = () => {
        showBottomSheet(() => (
            <View className="gap-5 px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
                <View className="gap-1">
                    <CustomText
                        tKey="emergencyCall"
                        style={fonts.fontSemiBold}
                        className="text-text17 text-secondary tracking-wide"
                    />

                    <CustomText
                        tKey="confirmHelpRequest"
                        style={fonts.fontRegular}
                        className="text-text15 text-grayText tracking-wide"
                    />
                </View>
                <View className="gap-3">
                    <CustomBtn
                        onPress={handleCallPress}
                        title={t('need_call')}
                        icon={<Ionicons name="call-outline" size={18} color="#fff" />}
                    />
                    <CustomBtn
                        onPress={hideBottomSheet}
                        title={t('close')}
                        backgroundColor="transparent"
                        textStyle={{
                            color: '#1F7575',
                        }}
                    />
                </View>
            </View>
        ), 'hide');
    }

    const handleCallPress = () => {
        hideBottomSheet();
        showBottomSheet(() => (
            <View className="w-full items-center gap-4 px-primary pt-primary" style={{ paddingBottom: bottomInset }}>
                <SuccessFailure
                    outerWidth={90}
                    outerHeight={90}
                    innerWidth={55}
                    status="success"
                    innerHeight={55}
                    iconSize={30}
                />
                <CustomText
                    tKey="thankYou"
                    style={fonts.fontSemiBold}
                    className="text-text17 tracking-wide text-secondary"
                />

                <CustomText
                    tKey="teamWillCall"
                    style={fonts.fontRegular}
                    className="text-text15 text-grayText tracking-wide"
                />

                <View className="w-full">
                    <CustomBtn onPress={hideBottomSheet} title={t('okay')} />
                </View>
            </View>
        ), 'hide');
    };

    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow title={`Ticket #${id}`} shadowBottom />

            <ScrollView
                contentContainerClassName="flex-grow px-primary pt-primary"
                contentContainerStyle={{ paddingBottom: bottomInset }}
                showsVerticalScrollIndicator={false}>

                <View className="flex-1 gap-5">
                    <Text
                        style={fonts.fontSemiBold}
                        className="text-text16 tracking-wide text-secondary">
                        {title}
                    </Text>
                    <Text style={fonts.fontRegular} className="text-text15 text-secondary tracking-wide">
                        {description}
                    </Text>
                    <View className="h-px w-full bg-border" />
                    <View className="gap-4">
                        <CustomText
                            tKey="ticketStatus"
                            style={fonts.fontSemiBold}
                            className="text-text16 tracking-wide text-secondary"
                        />
                        <Badge status="resolved" />
                    </View>
                </View>

                <View className="gap-3">
                    <CustomBtn onPress={openBottomSheet} title={t('resubmit')} />
                    <CustomBtn
                        onPress={router.back}
                        title={t('close')}
                        backgroundColor="#fff"
                        textStyle={{
                            color: '#1F7575',
                        }}
                    />
                </View>

            </ScrollView>

        </View>
    );
};

export default ParticularTicket;
