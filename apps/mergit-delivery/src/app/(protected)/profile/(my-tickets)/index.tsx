import { View } from 'react-native';
import { TitleHeader } from 'components/common';
import { FilterDropdown, TicketList } from 'components/profile/my-tickets';
import { useTranslation } from 'react-i18next';

const MyTicket = () => {
    const { t } = useTranslation();
    return (
        <View className="flex-1 bg-white">
            <TitleHeader backArrow title={t('my_tickets')} shadowBottom />
            <FilterDropdown />
            <TicketList />
        </View>
    );
};

export default MyTicket;
