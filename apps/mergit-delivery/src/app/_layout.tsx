import { QueryClientProvider } from '@tanstack/react-query';
import { Stack } from 'expo-router';
import i18n from 'localization/i18n';
import { I18nextProvider } from 'react-i18next';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { queryClient, startTokenRefreshScheduler, stopTokenRefreshScheduler } from 'helpers';
import { BottomSheetProvider, CustomModalProvider } from '@mergit-mobile/components';
import { CustomToastProvider, GlobalProvider, SocketProvider } from 'context';
import { useAuthStore } from '~/services/auth/authStore';
import { useTabsStore } from '~/services/tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useEffect } from 'react';
import { Keyboard, Platform } from 'react-native';
import { OneSignal } from 'react-native-onesignal';

const oneSignalId = process.env.EXPO_PUBLIC_ONESIGNAL_APP_ID || '';
OneSignal.initialize(oneSignalId);

export default function RootLayout() {
    const { token } = useAuthStore();
    const { setBottomInset } = useTabsStore();
    const { bottom } = useSafeAreaInsets();

    useEffect(() => {
        if (token) {
            startTokenRefreshScheduler();
        } else {
            stopTokenRefreshScheduler();
        }
        return () => {
            stopTokenRefreshScheduler();
        };
    }, [token]);

    useEffect(() => {
        const subscribeHide = Keyboard.addListener('keyboardDidHide', () => {
            if (Platform.OS === 'android') {
                setBottomInset(bottom + 8);
            } else {
                setBottomInset(25);
            }
        });
        const subscribeShow = Keyboard.addListener('keyboardDidShow', () => {
            setBottomInset(20);
        });

        return () => {
            subscribeHide.remove();
            subscribeShow.remove();
        };
    }, []);

    return (
        <GestureHandlerRootView style={{ flex: 1, backgroundColor: '#fff' }}>
            <I18nextProvider i18n={i18n}>
                <GlobalProvider>
                    <QueryClientProvider client={queryClient}>
                        <SocketProvider>
                            <KeyboardProvider>
                                <CustomToastProvider>
                                    <BottomSheetProvider>
                                        <CustomModalProvider>
                                            <Stack
                                                screenOptions={{
                                                    headerShown: false,
                                                    animation: 'slide_from_right',
                                                }}>
                                                <Stack.Protected guard={!token}>
                                                    <Stack.Screen name="(auth)" />
                                                </Stack.Protected>
                                                <Stack.Protected guard={!!token}>
                                                    <Stack.Screen name="(protected)" />
                                                </Stack.Protected>
                                                <Stack.Screen name="policy" />
                                            </Stack>
                                        </CustomModalProvider>
                                    </BottomSheetProvider>
                                </CustomToastProvider>
                            </KeyboardProvider>
                        </SocketProvider>
                    </QueryClientProvider>
                </GlobalProvider>
            </I18nextProvider>
        </GestureHandlerRootView>
    );
}
