import { createContext, PropsWithChildren, useEffect, useRef } from 'react';
import * as Location from 'expo-location';
import { io, Socket } from 'socket.io-client';
import { DefaultEventsMap } from '@socket.io/component-emitter';
import { useLocationStore } from '~/services/location';
import { useOrderStore } from 'services/orders';
import { Toast } from './CustomToastProvider';
import { useBottomSheet } from '@mergit-mobile/components';
import { NewOrder } from '../components/home/<USER>';

type SocketState = {
    socket: Socket<DefaultEventsMap, DefaultEventsMap>;
};

export const SocketContext = createContext<SocketState>({
    socket: {} as Socket<DefaultEventsMap, DefaultEventsMap>,
});

export const SocketProvider = ({ children }: PropsWithChildren) => {
    const { updateDeliveryPartnerLocation, deliveryPartnerId, isEnabled } = useLocationStore();
    const { updateNewSellers, updateNewOrderAssigned, updateOrderAssigned } =
        useOrderStore();
    const { showBottomSheet } = useBottomSheet();

    const socketRef = useRef<Socket<DefaultEventsMap, DefaultEventsMap> | null>(null);

    const openNewOrderSheet = () => {
        showBottomSheet(() => <NewOrder />);
    }

    useEffect(() => {
        if (isEnabled) {
            if (!socketRef.current) {
                socketRef.current = io(process.env.EXPO_PUBLIC_SOCKET_URL!, {
                    transports: ['websocket'],
                    autoConnect: true,
                    reconnection: true,
                    reconnectionAttempts: 10,
                    reconnectionDelay: 2000,
                });
            }

            return () => {
                if (socketRef.current) {
                    socketRef.current.disconnect();
                    socketRef.current = null;
                }
            };
        }
        return () => { };
    }, [isEnabled]);

    useEffect(() => {
        const socket = socketRef.current;
        if (!isEnabled || !socket) return;

        // getOrders();

        socket.emit('registerDeliveryPartner', {
            partnerId: deliveryPartnerId,
        });

        socket.on('connect', () => {
            console.log('socket connected:', socket.id);
        });

        socket.on('disconnect', () => {
            console.log('socket disconnected');
        });

        socket.on('connect_error', (err: any) => {
            console.log('Connect error:', err.message);
        });

        socket.on('zoneAlert', (data: any) => {
            Toast.show({
                message: data.message,
            });
        });

        socket.on('orderAssigned', (data: any) => {
            updateOrderAssigned(data.orderData);
            const sellers = data?.orderData
                .map((seller: any) => ({
                    name: seller.seller.name,
                    address: seller.seller.address,
                    preparingTime: seller.seller.preparingTime,
                }))
                .sort(
                    (a: any, b: any) =>
                        Number(a.preparingTime.value) - Number(b.preparingTime.value)
                );
            updateNewSellers(sellers);
            openNewOrderSheet();
        });

        socket.on('newOrderAssigned', (data: any) => {
            updateNewOrderAssigned(data.orderData);
        });

        socket.on('statusUpdate', (data: any) => {
            console.log('status update : ', data);
        });

        const pingInterval = setInterval(() => {
            socket.emit('custom-ping');
        }, 20000);

        socket.on('custom-pong', (timestamp: string) => {
            if (!timestamp || timestamp.length < 1) {
                console.log('Re-registering on pong...');
                socket.on('connect', () => {
                    console.log('socket connected:', socket.id);
                });
                socket.emit('registerDeliveryPartner', {
                    partnerId: deliveryPartnerId,
                });
            }
        });

        let locationSubscription: Location.LocationSubscription;

        const startWatchingLocation = async () => {
            const { status } = await Location.requestForegroundPermissionsAsync();
            if (status !== 'granted') {
                console.log('Permission to access location was denied');
                return;
            }

            locationSubscription = await Location.watchPositionAsync(
                {
                    accuracy: Location.Accuracy.BestForNavigation,
                    timeInterval: 5000,
                },
                (currentLocation) => {
                    updateDeliveryPartnerLocation(currentLocation);

                    socket.emit('partnerLocationUpdate', {
                        partnerId: deliveryPartnerId,
                        location: {
                            lat: currentLocation.coords.latitude,
                            lng: currentLocation.coords.longitude,
                        },
                    });
                }
            );
        };

        startWatchingLocation();

        return () => {
            if (locationSubscription) {
                locationSubscription.remove();
            }
            clearInterval(pingInterval);
            socket.off('connect');
            socket.off('disconnect');
            socket.off('connect_error');
            socket.off('zoneAlert');
            socket.off('orderAssigned');
            socket.off('newOrderAssigned');
            socket.off('statusUpdate');
            socket.off('custom-pong');
        };
    }, [isEnabled]);

    return (
        <SocketContext.Provider value={{ socket: socketRef.current! }}>
            {children}
        </SocketContext.Provider>
    );
};
